@extends('adminlte::page')

@section('title', 'AYD Admin')

@section('content_header')
    <h1>Administración</h1>
@stop

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">META</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.update.company.information') }}">
                        @csrf
                        <div class="form-group">
                            <label for="exampleInputEmail1">Descripción de la empresa:</label>
                            <textarea class="form-control" id="meta_description" name="meta_description" rows="5"> {{ $companyInformation->meta_description }} </textarea>
                        </div>
                        <div class="form-group">
                            <label for="exampleInputEmail1">Palabras clave (Keywords):</label>
                            <span class="text-muted">Separe por coma (,) cada palabra clave</span>
                            <textarea class="form-control" id="meta_keywords" name="meta_keywords" rows="5">{{ $companyInformation->meta_keywords }}</textarea>
                        </div>
                        <button type="submit" class="btn btn-primary btn-block">Actualizar meta información</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
@stop

@section('js')
@stop