@extends('adminlte::page')

@section('title', 'Crear formación profesional')

@section('content_header')
    <h1>Crear nueva formación profesional</h1>
@stop

@section('content')
    <div class="row">
        <div class="col-12 col-md-12">
            <div class="card card-outline card-success">
                <div class="card-header">
                    <h3 class="card-title">Nueva formación profesional</h3>
                </div>
                <div class="card-body">
                    {{-- Formulario para crear un nuevo detalle --}}
                    <form action="{{ route('admin.pages.professional.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        {{-- Título --}}
                        <div class="form-group">
                            <label for="name">Nombre: <span>*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                id="name" name="name"     
                                value="{{ old('name') }}" 
                                placeholder="Título de la formación profesional" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        {{-- Contenido --}}
                        <div class="form-group">
                            <label for="content">Contenido: <span>*</span></label>
                            <textarea class="form-control @error('content') is-invalid @enderror"   
                                id="content" name="content" 
                                rows="5">{{ old('content') }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <hr>

                        {{-- Imagen de portada --}}
                        <div class="form-group">
                            <label for="image">Imagen: <span>*</span></label>
                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" id="image">Imagen de portada</span>
                                </div>
                                <div class="custom-file">
                                    <input type="file" name="image" 
                                        class="custom-file-input @error('image') is-invalid @enderror" 
                                        id="image" aria-describedby="image">
                                    <label class="custom-file-label" for="image">Buscar imagen</label>
                                    @error('image')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                        <p>{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                            <p>Formatos permitidos: jpeg, png, jpg, gif, svg y webp.</p>
                        </div>

                        <button class="btn btn-success btn-block" type="submit">Guardar nuevo detalle</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
@stop

@section('js')
<script>
    tinymce.init({
        selector: 'textarea',
        plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
        toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat',
        language: 'es',
        height: 400
    });
</script>
@stop