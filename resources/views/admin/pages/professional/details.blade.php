@extends('adminlte::page')

@section('title', 'Descripción de consultorías')

@section('content_header')
    <div class="row mb-2">
        <div class="col-md-6">
            <div class="btn-group">
                <h1 class="m-0">{{ $professional->name }}</h1></h1>
                <button type="button" class="btn btn-light dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-expanded="false">
                    <span class="sr-only">Opciones</span>
                </button>
                <div class="dropdown-menu">
                    <a href="" class="dropdown-item btn-danger" onclick="event.preventDefault(); document.getElementById('delete-form').submit();">Eliminar formación profesional</a>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <ol class="breadcrumb float-sm-right">
                <li class="breadcrumb-item"><a href="{{ route('admin.pages.professional') }}">Formación profesional</a></li>
                <li class="breadcrumb-item active">{{ $professional->name }}</li>
            </ol>
        </div>
    </div>
    <form id="delete-form" action="{{ route('admin.pages.professional.delete', $professional->id) }}" method="POST">
        @csrf
        @method('DELETE')
    </form>
@stop

@section('content')
    <div class="row">
        <div class="col-12 col-md-12">
            <div class="card card-outline card-warning">
                <div class="card-header">
                    <h3 class="card-title">Detalles</h3>
                </div>
                <div class="card-body">
                    {{-- Formulario principal para actualizar --}}
                    <form action="{{ route('admin.pages.professional.update', $professional->id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        {{-- Título --}}
                        <div class="form-group">
                            <label for="name">Nombre formación profesional: <span>*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                id="name" name="name"     
                                value="{{ old('name', $professional->name) }}" 
                                placeholder="Formación profesional" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        {{-- Contenido --}}
                        <div class="form-group">
                            <label for="content">Contenido: <span>*</span></label>
                            <textarea class="form-control @error('content') is-invalid @enderror"   
                                id="content" name="content" 
                                rows="5">{{ old('content', $professional->content) }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <hr>

                        {{-- Galería de imágenes (campo para subir múltiples) --}}
                        <div class="form-group">
                            <label for="professional_images">Imágen de formación profesional:</label>
                            {{-- Imagen actual --}}
                            @if ($professional->image)
                                <div class="mb-3">
                                    <p><strong>Imagen actual:</strong></p>
                                    <img src="{{ asset($professional->image) }}" alt="{{ $professional->name }}" class="img-thumbnail" style="max-width: 300px;">
                                </div>
                            @endif
                            <p class="text-muted">Selecciona una imágen:</p>
                            <div class="input-group mb-3">
                                <div class="custom-file">
                                    <input type="file" name="image" 
                                        class="custom-file-input @error('image') is-invalid @enderror" 
                                        id="image">
                                    <label class="custom-file-label" for="image">Buscar imágenes...</label>
                                    @error('image')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-primary btn-block" type="submit">Actualizar {{ $professional->name }}</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
@stop

@section('js')
<script>
    tinymce.init({
        selector: 'textarea',
        plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
        toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat',
        language: 'es',
        height: 400
    });
</script>
@stop