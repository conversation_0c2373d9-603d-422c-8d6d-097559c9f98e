@extends('adminlte::page')

@section('title', 'Formación Profesional')

@section('content_header')
     <div class="row">
        <div class="col-md-6">
            <h1>Formación Profesional</h1>
        </div>
        <div class="col-md-6">
            <a href="{{ route('admin.pages.professional.create') }}" class="btn btn-success float-right">Nueva formación</a>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-12 col-md-12">
            <div class="card card-outline card-primary">
                <div class="card-header">
                    <h3 class="card-title">Formación Profesional</h3>
                </div>
                <div class="card-body">
                    <table class="table table-fixed table-responsive-lg table-hover" id="tablaBanners">
                        <thead>
                            <tr>
                                <th>Imagen</th>
                                <th>Titulo</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody id="sortable-banners">
                            @forelse ($profesional as $detalle)
                                <tr>
                                    <td>
                                        <img src="{{ asset($detalle->image) }}" alt="{{ $detalle->name }}" width="50px">
                                    </td>
                                    <td>{{ $detalle->name }}</td>
                                    <td align="center">
                                        <a href="{{ route('admin.pages.professional.view.details', $detalle->id) }}" class="btn btn-warning"><i class="fas fa-edit"></i></a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="3" class="text-center">No hay registros de formación profesional.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
<style>
    table th, table td {
        vertical-align: middle !important;
    }
</style>
@stop

@section('js')
@stop