@extends('adminlte::page')

@section('title', 'Consultorías')

@section('content_header')
    <div class="row">
        <div class="col-md-6">
            <h1>Consultorías</h1>
        </div>
        <div class="col-md-6">
            <a href="{{ route('admin.pages.consulting.create.details') }}" class="btn btn-success float-right">Nueva consultoría</a>
        </div>
    </div>
@stop

@section('content')
    {{-- editar contenido de consultorias --}}
    <div class="row">
        <div class="col-12 col-md-12">
            <div class="card card-outline card-primary">
                <div class="card-header">
                    <h3 class="card-title">Descripción de consultorias</h3>
                </div>
                <div class="card-body table-responsive">
                    <table class="table table-fixed table-responsive-lg table-hover">
                        <thead>
                            <tr>
                                <th>Nombre</th>
                                <th>Sub titulo</th>
                                <th>Titulo</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($secciones as $consultoria)
                                <tr>
                                    <td>{{ $consultoria->name }}</td>
                                    <td>{{ $consultoria->sub_title }}</td>
                                    <td>{{ Str::limit($consultoria->title, 50, '...') }}</td>
                                    <td>
                                        <a href="{{ route('admin.pages.consulting.view.section', $consultoria->id) }}" class="btn btn-warning"><i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {{-- detalles de consultorias --}}
    <div class="row">
        <div class="col-12 col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Detalles de consultorias</h3>
                </div>
                <div class="card-body table-responsive">
                    <table class="table table-fixed" id="tablaConsultorias">
                        <thead>
                            <tr>
                                <th>Titulo</th>
                                <th>Imagen?</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($detalleConsultorias as $detalles)
                                <tr>
                                    <td>{{ $detalles->title }}</td>
                                    <td>{{ $detalles->images->count() > 0 ? 'Si' : 'No' }}</td>
                                    <td align="center"><a href="{{ route('admin.pages.consulting.view.details', $detalles->id) }}" class="btn btn-warning"><i class="fas fa-edit"></i></a></td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
<style>
    table th, table td {
        vertical-align: middle !important;
    }
</style>
@stop

@section('js')
<script>
    $(document).ready(function() {
        $('#tablaConsultorias').DataTable( {
            language: {
                "search": "Buscar",
                "zeroRecords": "No se encontraron elementos coincidentes",
                "lengthMenu": "Mostrar _MENU_ elementos",
                "loadingRecords": "Cargando...",
                "processing": "Procesando...",
                "info": "Mostrando del _START_ al _END_ de _TOTAL_ elementos",
                "infoEmpty": "Mostrando del 0 al 0 de 0 elementos",
                "infoFiltered": "(filtrados de _MAX_ elementos totales)",
                "paginate": {
                    "previous": '<i class="fas fa-chevron-left"></i>',
                    "next": '<i class="fas fa-chevron-right"></i>',
                    "first": "Inicio",
                    "last": "Fin"
                }
            },
            "lengthMenu": [[20,30,50, -1], [20,30,50, "Todo"]],
            dom: 'lfrtip',
            buttons: [
                'copy',
                'csv',
                'excel',
                'pdf',
            ]
        });
    });
</script>
@stop