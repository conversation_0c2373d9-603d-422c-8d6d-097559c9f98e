@extends('adminlte::page')

@section('title', 'Descripción de consultorías')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <div class="btn-group">
                    <h1 class="m-0">{{ $subsection->title }}</h1></h1>
                    <button type="button" class="btn btn-light dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-expanded="false">
                        <span class="sr-only">Opciones</span>
                    </button>
                    <div class="dropdown-menu">
                        <a href="" class="dropdown-item btn-danger" onclick="event.preventDefault(); document.getElementById('delete-form').submit();">Eliminar consultoria</a>
                    </div>
                </div>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('admin.pages.consulting') }}">Consultorías</a></li>
                    <li class="breadcrumb-item active">{{ $subsection->title }}</li>
                </ol>
            </div>
        </div>
    </div>
    <form id="delete-form" action="{{ route('admin.pages.consulting.delete.detail', $subsection->id) }}" method="POST">
        @csrf
        @method('DELETE')
    </form>
@stop

@section('content')
    <div class="row">
        <div class="col-12 col-md-12">
            <div class="card card-outline card-warning">
                <div class="card-header">
                    <h3 class="card-title">Detalles</h3>
                </div>
                <div class="card-body">
                    {{-- Formulario principal para actualizar --}}
                    <form action="{{ route('admin.pages.consulting.update.details', $subsection->id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        {{-- Título --}}
                        <div class="form-group">
                            <label for="title">Título: <span>*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                id="title" name="title"     
                                value="{{ old('title', $subsection->title) }}" 
                                placeholder="Título de la subsección" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        {{-- Contenido --}}
                        <div class="form-group">
                            <label for="content">Contenido: <span>*</span></label>
                            <textarea class="form-control @error('content') is-invalid @enderror"   
                                id="content" name="content" 
                                rows="5">{{ old('content', $subsection->content) }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <hr>

                        {{-- Galería de imágenes (campo para subir múltiples) --}}
                        <div class="form-group">
                            <label for="subsection_images">Galería de imágenes:</label>
                            <p class="text-muted">Selecciona una o varias imágenes para la galería. Puedes subir nuevos archivos o eliminar los existentes.</p>
                            <div class="input-group mb-3">
                                <div class="custom-file">
                                    <input type="file" name="subsection_images[]" 
                                        class="custom-file-input @error('subsection_images.*') is-invalid @enderror" 
                                        id="subsection_images" multiple>
                                    <label class="custom-file-label" for="subsection_images">Buscar imágenes...</label>
                                    @error('subsection_images.*')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-primary btn-block" type="submit">Actualizar {{ $subsection->title }}</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Galería de imágenes</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach ($subsection->images as $image)
                            <div class="col-md-3">
                                <div class="image-wrapper" style="position: relative;">
                                    <img src="{{ asset($image->image) }}" alt="{{ $image->alt }}" class="img-thumbnail" style="max-width: 100%;">
                                    <form action="{{ route('admin.pages.delete.subsection.image', $image->id) }}" method="POST" style="position: absolute; top: 0; right: 0;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Estás seguro de que quieres eliminar esta imagen?');" title="Eliminar imagen">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
@stop

@section('js')
<script>
    tinymce.init({
        selector: 'textarea',
        plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
        toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat',
        language: 'es',
        height: 400
    });
</script>
@stop