@extends('adminlte::page')

@section('title', 'Crear detalle de consultoría')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark">
                    Crear nueva consultoría
                </h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('admin.pages.consulting') }}">Consultorías</a></li>
                    <li class="breadcrumb-item active">Nueva consultoría</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-12 col-md-12">
            <div class="card card-outline card-success">
                <div class="card-header">
                    <h3 class="card-title">Nuevo detalle</h3>
                </div>
                <div class="card-body">
                    {{-- Formulario para crear un nuevo detalle --}}
                    <form action="{{ route('admin.pages.consulting.store.details') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        {{-- Título --}}
                        <div class="form-group">
                            <label for="title">Título: <span>*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                id="title" name="title"     
                                value="{{ old('title') }}" 
                                placeholder="Título del detalle" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        {{-- Contenido --}}
                        <div class="form-group">
                            <label for="content">Contenido: <span>*</span></label>
                            <textarea class="form-control @error('content') is-invalid @enderror"   
                                id="content" name="content" 
                                rows="5">{{ old('content') }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <hr>

                        {{-- Galería de imágenes (campo para subir múltiples) --}}
                        <div class="form-group">
                            <label for="subsection_images">Galería de imágenes:</label>
                            <p class="text-muted">Selecciona una o varias imágenes para la galería.</p>
                            <div class="input-group mb-3">
                                <div class="custom-file">
                                    <input type="file" name="subsection_images[]" 
                                        class="custom-file-input @error('subsection_images.*') is-invalid @enderror" 
                                        id="subsection_images" multiple>
                                    <label class="custom-file-label" for="subsection_images">Buscar imágenes...</label>
                                    @error('subsection_images.*')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <button class="btn btn-success btn-block" type="submit">Guardar nueva consultoría</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
@stop

@section('js')
<script>
    tinymce.init({
        selector: 'textarea',
        plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
        toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat',
        language: 'es',
        height: 400
    });
</script>
@stop