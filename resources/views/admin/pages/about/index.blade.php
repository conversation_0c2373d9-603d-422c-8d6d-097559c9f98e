@extends('adminlte::page')

@section('title', 'Acerca de nosotros')

@section('content_header')
    <h1 class="m-0 text-dark">Secciones de la página "Acerca de nosotros"</h1>
@stop

@section('content')
    <div class="row">
        {{-- SECCIÓN: ACERCA DE NOSOTROS --}}
        <div class="col-12">
            <div class="card card-outline card-primary">
                <div class="card-header">
                    <h3 class="card-title">Secciones de la página</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <table class="table table-hover table-responsive-lg" id="tablaSeccionesNosotros">
                        <thead>
                            <tr>
                                <th>Nombre</th>
                                <th>T<PERSON>tu<PERSON></th>
                                <th>Subtítulo</th>
                                <th class="text-center">Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($seccionesNosotros as $seccion)
                                <tr>
                                    <td>{{ $seccion->name }}</td>
                                    <td>{{ $seccion->title }}</td>
                                    <td>{{ $seccion->sub_title }}</td>
                                    <td class="text-center">
                                        <a href="{{ route('admin.pages.about.view.section', $seccion->id) }}" class="btn btn-sm btn-warning" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="4" class="text-center">No hay secciones registradas.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        {{-- SECCIÓN: VALORES --}}
        <div class="col-12">
            <div class="card card-outline card-info">
                <div class="card-header">
                    <h3 class="card-title">Valores</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <table class="table table-hover table-responsive-lg" id="tablaValores">
                        <thead>
                            <tr>
                                <th>Icono</th>
                                <th>Nombre</th>
                                <th>Descripción</th>
                                <th>Imagen</th>
                                <th class="text-center">Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($valores as $valor)
                                <tr>
                                    <td><i class="{{ $valor->icon }} fa-2x"></i></td>
                                    <td>{{ $valor->name }}</td>
                                    <td>{{ Str::limit($valor->description, 50, '...') }}</td>
                                    <td><img src="{{ asset($valor->image) }}" alt="{{ $valor->name }}" class="img-thumbnail" width="60px"></td>
                                    <td class="text-center">
                                        <a href="{{ route('admin.pages.about.view.corporate', $valor->id) }}" class="btn btn-sm btn-warning" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="5" class="text-center">No hay registros de valores.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
<style>
    table th, table td {
        vertical-align: middle !important;
    }
</style>
@stop

@section('js')

@stop