@extends('adminlte::page')

@section('title', ''. $valor->name)

@section('content_header')
    <div class="row mb-2">
        <div class="col-md-6">
            <div class="btn-group">
                <h1 class="m-0">{{ $valor->name }}</h1>
            </div>
        </div>
        <div class="col-md-6">
            <ol class="breadcrumb float-sm-right">
                <li class="breadcrumb-item"><a href="{{ route('admin.pages.about') }}">Nosotros</a></li>
                <li class="breadcrumb-item active">{{ $valor->name }}</li>
            </ol>
        </div>
    </div>
@stop

@section('content')
    {{-- editar contenido de sección --}}
    <div class="row">
        <div class="col-12 col-md-12">
            <div class="card card-outline card-primary">
                <div class="card-header">
                    <h3 class="card-title">Editar: {{ $valor->name }}</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.pages.about.update.corporate', $valor->id) }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        {{-- Nombre --}}
                        <div class="form-group">
                            <label for="name">Nombre: <span>*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                id="name" name="name"     
                                value="{{ old('name', $valor->name) }}" 
                                placeholder="Nombre del valor" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        {{-- Icono --}}
                        <div class="form-group">
                            <label for="icon">Icono: <span>*</span></label>
                            <input type="text" class="form-control @error('icon') is-invalid @enderror" 
                                id="icon" name="icon"     
                                value="{{ old('icon', $valor->icon) }}" 
                                placeholder="Icono del valor" required>
                            <span class="text-muted">Ejemplo: fas fa-briefcase. Puede buscar iconos en <a href="https://fontawesome.com/v5/search?ic=free&o=r" target="_blank">https://fontawesome.com/icons</a></span>
                            @error('icon')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        {{-- Descripción --}}
                        <div class="form-group">
                            <label for="description">Descripción: <span>*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror"   
                                id="description" name="description" 
                                rows="10">{{ old('description', $valor->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        @if ($valor->image)
                            {{-- Imagen actual --}}
                            <div class="form-group">
                                <p>Imagen actual: <img src="{{ asset($valor->image) }}" alt="{{ $valor->name }}" width="300px"></p>
                            </div>
                            {{-- Imagen --}}
                            <div class="form-group">
                                <label for="image">Imagen: <span>*</span></label>
                                <div class="input-group mb-3">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" id="image">Nueva imagen</span>
                                    </div>
                                    <div class="custom-file">
                                        <input type="file" name="image" 
                                            class="custom-file-input @error('image') is-invalid @enderror" 
                                            id="image" aria-describedby="image">
                                        <label class="custom-file-label" for="image">Buscar imagen</label>
                                        @error('image')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <p>Formatos permitidos: jpeg, png, jpg, gif, svg y webp.</p>
                            </div>
                        @endif
                        <button class="btn btn-primary btn-block" type="submit">Actualizar {{ $valor->name }}</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

@stop

@section('css')
@stop

@section('js')
@stop