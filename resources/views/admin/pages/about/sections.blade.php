@extends('adminlte::page')

@section('title', ''. $section->title)

@section('content_header')
    <div class="row mb-2">
        <div class="col-md-6">
            <div class="btn-group">
                <h1 class="m-0">{{ $section->name }} / {{ $section->title }}</h1>
            </div>
        </div>
        <div class="col-md-6">
            <ol class="breadcrumb float-sm-right">
                <li class="breadcrumb-item"><a href="{{ route('admin.pages.about') }}">Nosotros</a></li>
                <li class="breadcrumb-item active">{{ $section->name }}</li>
            </ol>
        </div>
    </div>
@stop

@section('content')
    {{-- editar contenido de sección --}}
    <div class="row">
        <div class="col-12 col-md-12">
            <div class="card card-outline card-warning">
                <div class="card-header">
                    <h3 class="card-title">Editar: {{ $section->title }}</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.pages.about.update.section', $section->id) }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        {{-- Título --}}
                        <div class="form-group">
                            <label for="title">Título: <span>*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                id="title" name="title"     
                                value="{{ old('title', $section->title) }}" 
                                placeholder="Título de la sección" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        {{-- Sub Título --}}
                        <div class="form-group">
                            <label for="sub_title">Sub título: <span>*</span></label>
                            <input type="text" class="form-control @error('sub_title') is-invalid @enderror" 
                                id="sub_title" name="sub_title" 
                                value="{{ old('sub_title', $section->sub_title) }}" 
                                placeholder="Sub título de la sección" required>
                            @error('sub_title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        @if ($section->cover_image)
                            {{-- Imagen --}}
                            <div class="form-group">
                                <label for="cover_image">Imagen: <span>*</span></label>
                                <div class="input-group mb-3">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" id="cover_image">Nueva imagen</span>
                                    </div>
                                    <div class="custom-file">
                                        <input type="file" name="cover_image" 
                                            class="custom-file-input @error('cover_image') is-invalid @enderror" 
                                            id="cover_image" aria-describedby="cover_image">
                                        <label class="custom-file-label" for="cover_image">Buscar imagen</label>
                                        @error('cover_image')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <p>Formatos permitidos: jpeg, png, jpg, gif y webp.</p>
                            </div>
                        @endif
                        {{-- Contenido --}}
                        <div class="form-group">
                            <label for="content">Contenido: <span>*</span></label>
                            <textarea class="form-control @error('content') is-invalid @enderror"   
                                id="content" name="content" 
                                rows="10">{{ old('content', $section->content) }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <br>
                        <button class="btn btn-primary btn-block" type="submit">Actualizar {{ $section->title }}</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @if ($section->subsections->isNotEmpty())
    <div class="row">
    @foreach ($section->subsections as $subsection)
        {{-- editar subsección --}}
        <div class="col-12 col-md-6">
            <div class="card card-outline card-warning">
                <div class="card-header">
                    <h3 class="card-title">Editar: {{ $subsection->title }}</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.pages.about.update.subsection', $subsection->id) }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        {{-- Título --}}
                        <div class="form-group">
                            <label for="title">Título: <span>*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                id="title" name="title"     
                                value="{{ old('title', $subsection->title) }}" 
                                placeholder="Título de la subsección" readonly>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        @if ($subsection->cover_image)
                            {{-- Imagen --}}
                            <div class="mb-3">
                                <p><strong>Imagen actual:</strong></p>
                                <img src="{{ asset($subsection->cover_image) }}"
                                alt="Imagen actual"
                                class="img-thumbnail"
                                style="max-width: 200px; max-height: 150px;">
                                <p class="text-muted mt-2">Selecciona una nueva imagen para reemplazar la actual</p>
                            </div>
                            <br>
                            <div class="form-group">
                                <label for="cover_image">Imagen: <span>*</span></label>
                                @if ($subsection->section_id == 2)
                                    <p style="color: red">Se recomienda mantener las mismas dimensiones en cada imagen de todas las sub secciones, para no alterar la vista.</p>
                                @endif
                                <div class="input-group mb-3">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" id="cover_image">Nueva imagen</span>
                                    </div>
                                    <div class="custom-file">
                                        <input type="file" name="cover_image" 
                                            class="custom-file-input @error('cover_image') is-invalid @enderror" 
                                            id="cover_image" aria-describedby="cover_image">
                                        <label class="custom-file-label" for="cover_image">Buscar imagen</label>
                                        @error('cover_image')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <p>Formatos permitidos: jpeg, png, jpg, gif y webp.</p>
                            </div>
                        @endif
                        {{-- Contenido --}}
                        <div class="form-group">
                            <label for="content">Contenido: <span>*</span></label>
                            <textarea class="form-control @error('content') is-invalid @enderror"   
                                id="content" name="content" 
                                rows="5">{{ old('content', $subsection->content) }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <br>
                        <button class="btn btn-primary btn-block" type="submit">Actualizar {{ $subsection->title }}</button>
                    </form>
                </div>
            </div>
        </div>
        @endforeach
    </div>
    @endif

    {{-- imagenes de seccion --}}
    @if ($section->section_images->isNotEmpty())
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Subir imágenes</h3>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('admin.pages.upload.about.gallery.images', $section->id) }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            {{-- Galería de imágenes (campo para subir múltiples) --}}
                            <div class="form-group">
                                <label for="images">Galería de imágenes:</label>
                                <p>Formatos permitidos: jpeg, png, jpg, gif y webp. Dimensión recomendada: 300x300px.</p>
                                <p class="text-muted">Selecciona una o varias imágenes para la galería. Puedes subir nuevos archivos o eliminar los existentes.</p>
                                <div class="input-group mb-3">
                                    <div class="custom-file">
                                        <input type="file" name="images[]" 
                                            class="custom-file-input @error('images.*') is-invalid @enderror" 
                                            id="images" multiple>
                                        <label class="custom-file-label" for="images">Buscar imágenes...</label>
                                        @error('images.*')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <button class="btn btn-primary btn-block" type="submit">Subir imágenes</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Galería de imágenes</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach ($section->section_images as $image)
                                <div class="col-md-3">
                                    <div class="image-wrapper" style="position: relative;">
                                        <img src="{{ asset($image->image) }}" alt="{{ $image->alt }}" class="img-thumbnail" style="max-width: 100%;"><p></p>
                                        <form action="{{ route('admin.pages.delete.section.image', $image->id) }}" method="POST" style="position: absolute; top: 0; right: 0;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Estás seguro de que quieres eliminar esta imagen?');" title="Eliminar imagen">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
    
@stop

@section('css')
@stop

@section('js')
@stop