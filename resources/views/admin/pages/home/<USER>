@extends('adminlte::page')

@section('title', 'Carrusel')

@section('content_header')
    <div class="row mb-2">
        <div class="col-md-12">
            <div class="btn-group">
                <h1 class="m-0">{{ $banner->title }}</h1></h1>
                <button type="button" class="btn btn-light dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-expanded="false">
                    <span class="sr-only">Opciones</span>
                </button>
                <div class="dropdown-menu">
                    <a href="" class="dropdown-item btn-danger" onclick="event.preventDefault(); document.getElementById('delete-form').submit();">Eliminar este banner</a>
                </div>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-12 col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Contenido actual</h3>
                </div>
                <div class="card-body">
                    <p>Titulo: {{ $banner->title }}</p>
                    <p>Contenido: {{ $banner->content }}</p>
                    <p>Orden: {{ $banner->order }}</p>
                    <p>Imagen: <img src="{{ asset($banner->img_banner) }}" alt="{{ $banner->title }}" width="300px"></p>
                    <form id="delete-form" action="{{ route('admin.pages.delete.banner', $banner->id) }}" method="POST">
                        @csrf
                        @method('DELETE')
                    </form>
                </div>
            </div>
        </div>
    </div>
    {{-- editar contenido de banner --}}
    <div class="row">
        <div class="col-12 col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Editar banner</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.pages.update.banner', $banner->id) }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        {{-- Título --}}
                        <div class="form-group">
                            <label for="title">Título: <span>*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                id="title" name="title" 
                                value="{{ old('title', $banner->title) }}" 
                                placeholder="Título del banner" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        {{-- Imagen --}}
                        <div class="form-group">
                            <label for="cover_image">Imagen: <span>*</span></label>
                            <p style="color: red">Se recomienda una imagen de 1920x1080px.</p>
                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" id="img_banner">Nueva imagen</span>
                                </div>
                                <div class="custom-file">
                                    <input type="file" name="img_banner" 
                                        class="custom-file-input @error('img_banner') is-invalid @enderror" 
                                        id="img_banner" aria-describedby="img_banner">
                                    <label class="custom-file-label" for="img_banner">Buscar imagen</label>
                                    @error('img_banner')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <p>Formatos permitidos: jpeg, png, jpg, gif y webp.</p>
                        </div>
                        {{-- Contenido --}}
                        <div class="form-group">
                            <label for="content">Contenido: <span>*</span></label>
                            <textarea class="form-control @error('content') is-invalid @enderror"   
                                id="content" name="content" 
                                rows="10">{{ old('content', $banner->content) }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <br>
                        <button class="btn btn-primary btn-block" type="submit">Actualizar banner</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

@stop

@section('css')
@stop

@section('js')
<script>
    const deleteButton = document.getElementById('deleteBanner');
    const confirmButtons = document.getElementById('confirmButtons');
    const confirmDeleteButton = document.getElementById('confirmDeleteBanner');
    const cancelButton = document.getElementById('cancelDeleteBanner');
    const deleteForm = document.getElementById('delete-form');

    deleteButton.addEventListener('click', () => {
        deleteButton.style.display = 'none';
        confirmButtons.style.display = 'block';
    });

    cancelButton.addEventListener('click', () => {
        confirmButtons.style.display = 'none';
        deleteButton.style.display = 'block';
    });

    confirmDeleteButton.addEventListener('click', (event) => {
        event.preventDefault();
        deleteForm.submit();
    });
</script>
@stop