@extends('adminlte::page')

@section('title', 'Inicio')

@section('content_header')
    <h1>Inicio</h1>
@stop

@section('content')
    {{-- editar contenido de inicio --}}
    {{-- banners --}}
    <div class="row">
        <div class="col-12 col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Banners</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                        <button type="button" class="btn btn-tool" data-card-widget="maximize">
                            <i class="fas fa-expand"></i>
                        </button>
                        <button class="btn-tool" data-toggle="modal" data-target="#modalAgregarBanner">Agregar</button>
                    </div>
                </div>
                <div class="card-body">
                    <table class="table table-fixed table-responsive-lg table-hover" id="tablaBanners">
                        <thead>
                            <tr>
                                <th>Orden</th>
                                <th>Titulo</th>
                                <th>Contenido</th>
                                <th>Imagen</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody id="sortable-banners">
                            @foreach ($banners as $banner)
                                <tr data-id="{{ $banner->id }}">
                                    <td style="cursor: move;">
                                        <i class="fas fa-arrows-alt"></i>
                                    </td>
                                    <td>{{ $banner->title }}</td>
                                    <td>{{ Str::limit($banner->content, 50, '...') }}</td>
                                    <td><img src="{{ asset($banner->img_banner) }}" alt="{{ $banner->title }}" width="50px"></td>
                                    <td align="center"><a href="{{ route('admin.pages.view.banner', $banner->id) }}" class="btn btn-warning"><i class="fas fa-edit"></i></a></td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    {{-- nosotros Inicio--}}
    <div class="row">
        <div class="col-12 col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Secciones de Inicio</h3>
                </div>
                <div class="card-body">
                    <table class="table table-fixed table-responsive-lg table-hover" id="tablaBanners">
                        <thead>
                            <tr>
                                <th>Nombre</th>
                                <th>Sub titulo</th>
                                <th>Titulo</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($secciones as $inicio)
                                <tr>
                                    <td>{{ $inicio->name }}</td>
                                    <td>{{ $inicio->sub_title }}</td>
                                    <td>{{ Str::limit($inicio->title, 50, '...') }}</td>
                                    <td align="center"><a href="{{ route('admin.pages.home.view.section', $inicio->id) }}" class="btn btn-warning"><i class="fas fa-edit"></i></a></td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {{-- modal para agregar banner --}}
    <div class="modal fade" id="modalAgregarBanner" tabindex="-1" role="dialog" aria-labelledby="modalAgregarBannerLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalAgregarBannerLabel">Agregar banner</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form method="POST" action="{{ route('admin.pages.store.banner') }}" enctype="multipart/form-data">
                        @csrf
                        {{-- Título --}}
                        <div class="form-group">
                            <label for="title">Título: <span>*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                id="title" name="title" 
                                value="{{ old('title') }}" 
                                placeholder="Título del banner" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        {{-- Imagen --}}
                        <div class="form-group">
                            <label for="img_banner">Imagen: <span>*</span></label>
                            <p style="color: red">Se recomienda una imagen de 1920x1080px.</p>
                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" id="img_banner">Nueva imagen</span>
                                </div>
                                <div class="custom-file">
                                    <input type="file" name="img_banner" 
                                        class="custom-file-input @error('img_banner') is-invalid @enderror" 
                                        id="img_banner" aria-describedby="img_banner">  
                                    <label class="custom-file-label" for="img_banner">Buscar imagen</label>
                                    @error('img_banner')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        {{-- Contenido --}}
                        <div class="form-group">
                            <label for="content">Contenido: <span>*</span></label>
                            <textarea class="form-control @error('content') is-invalid @enderror"   
                                id="content" name="content" 
                                rows="10">{{ old('content') }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <br>
                        <button class="btn btn-primary btn-block" type="submit">Crear banner</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
{{-- para ordenar banner --}}
<style>
    /* Estilo para el cursor de arrastrar */
    .sortable-handle {
        cursor: move;
    }
    table th, table td {
        vertical-align: middle !important;
    }
</style>
@stop

@section('js')
{{-- para ordenar banner --}}
{{-- Sortable.js --}}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.14.0/Sortable.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const sortableList = document.getElementById('sortable-banners');

        const sortable = new Sortable(sortableList, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            onEnd: function (evt) {
                const newOrder = Array.from(sortableList.children).map(row => row.dataset.id);
                fetch('{{ route('admin.banners.update.order') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ banner_order: newOrder })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        alert(data.message);
                    } else if (data.error) {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error en la conexión. No se pudo actualizar el orden.');
                });
            }
        });
    });
</script>
@stop