@extends('adminlte::page')

@section('title', 'Master contenido')

@section('content_header')
    <h1>Master contenido</h1>
@stop

@section('content')
{{-- secciones --}}
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Secciones</h3>
                </div>
                <div class="card-body">
                    <table class="table table-fixed table-responsive-lg table-hover" id="tablaBanners">
                        <thead>
                            <tr>
                                <th>Nombre</th>
                                <th>Titulo</th>
                                <th>Subtitulo</th>
                                <th align="center">Acciones</th>
                            </tr>
                        </thead>
                        <tbody id="sortable-banners">
                            @forelse ($secciones as $seccion)
                                <tr>
                                    <td>{{ $seccion->name }}</td>
                                    <td>{{ $seccion->title }}</td>
                                    <td>{{ $seccion->sub_title }}</td>
                                    <td align="center">
                                        <a href="{{ route('admin.pages.master.view.details', $seccion->id) }}" class="btn btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="3" class="text-center">No hay registros de formación profesional.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Headers por página</h3>
                </div>
                <div class="card-body">
                    <table class="table table-fixed table-responsive-lg table-hover" id="tablaBanners">
                        <thead>
                            <tr>
                                <th>Imagen</th>
                                <th>Páginas</th>
                                <th>Titulo</th>
                                <th align="center">Acciones</th>
                            </tr>
                        </thead>
                        <tbody id="sortable-banners">
                            @forelse ($headers as $header)
                                <tr>
                                    <td>
                                        <img src="{{ asset($header->img_header) }}" alt="{{ $header->page->name }}" width="50px">
                                    </td>
                                    <td>{{ $header->page->name }}</td>
                                    <td>{{ $header->title }}</td>
                                    <td align="center">
                                        <a href="{{ route('admin.pages.header.view.details', $header->id) }}" class="btn btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="3" class="text-center">No hay registros de formación profesional.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
<style>
    table th, table td {
        vertical-align: middle !important;
    }
</style>
@stop

@section('js')
@stop