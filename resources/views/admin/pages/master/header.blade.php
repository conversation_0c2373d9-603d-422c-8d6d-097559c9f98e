@extends('adminlte::page')

@section('title', 'Header de página')

@section('content_header')
    <div class="row mb-2">
        <div class="col-md-6">
            <div class="btn-group">
                <h1 class="m-0">{{ $header->page->name }} - {{ $header->title }}</h1>
            </div>
        </div>
        <div class="col-md-6">
            <ol class="breadcrumb float-sm-right">
                <li class="breadcrumb-item"><a href="{{ route('admin.pages.master') }}">Master</a></li>
                <li class="breadcrumb-item active">{{ $header->title }}</li>
            </ol>
        </div>
    </div>
@stop

@section('content')
    {{-- editar contenido de banner --}}
    <div class="row">
        <div class="col-12 col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Editar header de la página: {{ $header->page->name }}</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.pages.header.update', $header->id) }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        {{-- Título --}}
                        <div class="form-group">
                            <label for="title">Título: <span>*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                id="title" name="title" 
                                value="{{ old('title', $header->title) }}" 
                                placeholder="Título del banner" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        {{-- Imagen --}}
                        {{-- Imagen actual --}}
                        <div class="form-group">
                            <p>Imagen actual: <img src="{{ asset($header->img_header) }}" alt="{{ $header->title }}" width="300px"></p>
                        </div>
                        <div class="form-group">
                            <label for="cover_image">Imagen: <span>*</span></label>
                            <p style="color: red">Se recomienda una imagen de 1920x500px.</p>
                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" id="img_header">Nueva imagen</span>
                                </div>
                                <div class="custom-file">
                                    <input type="file" name="img_header" 
                                        class="custom-file-input @error('img_header') is-invalid @enderror" 
                                        id="img_header" aria-describedby="img_header">
                                    <label class="custom-file-label" for="img_header">Buscar imagen</label>
                                    @error('img_header')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <p>Formatos permitidos: jpeg, png, jpg, gif y webp.</p>
                        </div>
                        <button class="btn btn-primary btn-block" type="submit">Actualizar encabezado de la página {{ $header->page->name }}</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

@stop

@section('css')
@stop

@section('js')

@stop