@extends('adminlte::page')

@section('title', 'Suscripciones')

@section('content_header')
    <h1>Usuarios suscriptos</h1>
@stop

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Listado de suscriptores al boletín</h3>
            </div>
            <div class="card-body table-responsive">
                <table class="table table-head-fixed text-nowrap" id="tablaSuscriptores">
                    <thead>
                        <tr>
                            <th>Nombre</th>
                            <th>Empresa</th>
                            <th>Email</th>
                            <th>Pais</th>
                            <th>Fecha de creación</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($newsletterSubscribers as $subscriber)
                            <tr>
                                <td><a href="{{ route('admin.edit.subscribers', $subscriber->id) }}">{{ $subscriber->full_name }}</a></td>
                                <td>{{ $subscriber->company }}</td>
                                <td>{{ $subscriber->email }}</td>
                                <td>{{ $subscriber->country }}</td>
                                <td>{{ $subscriber->created_at }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
@stop

@section('js')
<script>
    $(document).ready(function() {
        $('#tablaSuscriptores').DataTable( {
            language: {
                "search": "Buscar",
                "zeroRecords": "No se encontraron elementos coincidentes",
                "lengthMenu": "Mostrar _MENU_ elementos",
                "loadingRecords": "Cargando...",
                "processing": "Procesando...",
                "info": "Mostrando del _START_ al _END_ de _TOTAL_ elementos",
                "infoEmpty": "Mostrando del 0 al 0 de 0 elementos",
                "infoFiltered": "(filtrados de _MAX_ elementos totales)",
                "paginate": {
                    "previous": '<i class="fas fa-chevron-left"></i>',
                    "next": '<i class="fas fa-chevron-right"></i>',
                    "first": "Inicio",
                    "last": "Fin"
                }
            },
            "lengthMenu": [[20,30,50, -1], [20,30,50, "Todo"]],
            dom: 'lBfrtip',
            buttons: [
                'copy',
                'csv',
                'excel',
                'pdf',
            ]
        });
    });
</script>
@stop