@extends('adminlte::page')

@section('title', 'Empleos')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <h1>Empleos</h1>
        <a href="{{ route('admin.view.new.job') }}" class="btn btn-primary">Nuevo empleo</a>
    </div>
@stop

@section('content')
<div class="row">
    <div class="col-12 col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Listado de empleos</h3>
            </div>
            <div class="card-body table-responsive">
                <table class="table table-head-fixed text-nowrap" id="tablaPublicaciones">
                    <thead>
                        <tr>
                            <th></th>
                            <th>Titulos del trabajo</th>
                            <th>Subtítulos/Area</th>
                            <th>Estado</th>
                            <th>Fecha de creación</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($jobs as $job)
                            <tr>
                                <td>
                                    <img src="{{ Storage::url($job->cover_image) }}" alt="{{ $job->title }}" width="50px">
                                </td>
                                <td>{{ $job->title }}</td>
                                <td>{{ $job->subtitle }}</td>
                                <td>
                                    <span class="badge badge-{{ $job->status->name == 'En línea' ? 'success' : 'warning' }}">{{ $job->status->name }}</span>
                                </td>
                                <td>{{ $job->created_at }}</td>
                                <td>
                                    <a href="{{ route('admin.job.view', $job->id) }}" class="btn btn-warning">
                                        <i class="fas fa-pen"></i>
                                    </a>
                                    <a href="{{ route('admin.job.view', $job->id) }}" class="btn btn-primary">
                                        <i class="fas fa-id-badge"></i>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
@stop

@section('js')
<script>
    $(document).ready(function() {
        $('#tablaPublicaciones').DataTable( {
            language: {
                "search": "Buscar",
                "zeroRecords": "No se encontraron elementos coincidentes",
                "lengthMenu": "Mostrar _MENU_ elementos",
                "loadingRecords": "Cargando...",
                "processing": "Procesando...",
                "info": "Mostrando del _START_ al _END_ de _TOTAL_ elementos",
                "infoEmpty": "Mostrando del 0 al 0 de 0 elementos",
                "infoFiltered": "(filtrados de _MAX_ elementos totales)",
                "paginate": {
                    "previous": '<i class="fas fa-chevron-left"></i>',
                    "next": '<i class="fas fa-chevron-right"></i>',
                    "first": "Inicio",
                    "last": "Fin"
                }
            },
            "lengthMenu": [[20,30,50, -1], [20,30,50, "Todo"]],
            dom: 'lfrtip',
            buttons: [
                'copy',
                'csv',
                'excel',
                'pdf',
            ]
        });
    });
</script>
@stop