@extends('adminlte::page')

@section('title', 'Nuevo empleo')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Editar publicación de empleo</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('admin.recruitment') }}">Empleos</a></li>
                    <li class="breadcrumb-item active">Edición de empleo</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Editar publicación de empleo</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('jobs.store') }}" enctype="multipart/form-data">
                        @csrf
                        {{-- Título --}}
                        <div class="form-group">
                            <label for="title">Título empleo: <span>*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                id="title" name="title" 
                                value="{{ old('title') }}" 
                                placeholder="Título del empleo" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        {{-- Descripción corta --}}
                        <div class="form-group">
                            <label for="short_description">Breve descripción: <span>*</span></label>
                            <input type="text" class="form-control @error('short_description') is-invalid @enderror" 
                                id="short_description" name="short_description" 
                                value="{{ old('short_description') }}" 
                                placeholder="Descripción corta del empleo" required>
                            @error('short_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>


                        <div class="row">

                            {{-- Sub Título/area --}}
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="subtitle">Subtítulo/area: <span>*</span></label>
                                    <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                                        id="subtitle" name="subtitle" 
                                        value="{{ old('subtitle') }}" 
                                        placeholder="Subítulo/Area del empleo" required>
                                    @error('subtitle')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            {{-- Estado --}}
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="status_id">Estado: <span>*</span></label>
                                    <select class="form-control @error('status_id') is-invalid @enderror" 
                                            id="status_id" name="status_id" required>
                                        <option value="">Seleccione un estado</option>
                                        @foreach($statuses as $status)
                                            <option value="{{ $status->id }}" {{ old('status_id') == $status->id ? 'selected' : '' }}>
                                                {{ $status->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('status_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        {{-- Imagen de portada --}}
                        <div class="form-group">
                            <label for="cover_image">Imagen de portada: <span>*</span></label>
                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" id="cover_image">Imagen de portada</span>
                                </div>
                                <div class="custom-file">
                                    <input type="file" name="cover_image" 
                                        class="custom-file-input @error('cover_image') is-invalid @enderror" 
                                        id="inputGroupFile01" aria-describedby="inputGroupFileAddon01">
                                    <label class="custom-file-label" for="inputGroupFile01">Buscar imagen</label>
                                    @error('cover_image')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <p>Formatos permitidos: jpeg, png, jpg, gif y webp.</p>
                        </div>

                        {{-- Contenido --}}
                        <div class="form-group">
                            <label for="description">Detalles del empleo/puesto: <span>*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" placeholder="Detalles del puesto/empleo" rows="10">{{ old('description') }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <br>
                        <button class="btn btn-primary btn-block" type="submit">Publicar</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')

@stop

@section('js')

@stop