@extends('adminlte::page')

@section('title', 'Editar empleo')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Editar publicación de empleo</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('admin.recruitment') }}">Empleos</a></li>
                    <li class="breadcrumb-item active">Edición de empleo</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-12">
            {{-- Información del empleo actual --}}
            <div class="card card-info mb-3">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle"></i> Información actual del empleo
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <p><strong>Título:</strong> {{ $job->title }}</p>
                            <p><strong>Subtítulo:</strong> {{ $job->subtitle ?? 'No especificado' }}</p>
                            <p><strong>Estado:</strong>
                                <span class="badge badge-{{ $job->status_id == 1 ? 'success' : ($job->status_id == 2 ? 'warning' : 'danger') }}">
                                    {{ $job->status->name ?? 'Sin estado' }}
                                </span>
                            </p>
                            <p><strong>URL:</strong> <code>{{ $job->slug }}</code></p>
                            <p><strong>Creado:</strong> {{ $job->created_at->format('d/m/Y H:i') }}</p>
                            <p><strong>Última actualización:</strong> {{ $job->updated_at->format('d/m/Y H:i') }}</p>
                        </div>
                        <div class="col-md-4 text-center">
                            @if($job->cover_image)
                                <img src="{{ asset('storage/' . $job->cover_image) }}"
                                     alt="Imagen actual"
                                     class="img-fluid rounded"
                                     style="max-height: 120px;">
                            @else
                                <div class="bg-light p-3 rounded">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                    <p class="text-muted mt-2">Sin imagen</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            {{-- Formulario de edición --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit"></i> Editar publicación de empleo
                    </h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('jobs.update', $job->id) }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        {{-- Título --}}
                        <div class="form-group">
                            <label for="title">Título empleo: <span>*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror"
                                id="title" name="title"
                                value="{{ old('title', $job->title) }}"
                                placeholder="Título del empleo" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        {{-- Descripción corta --}}
                        <div class="form-group">
                            <label for="short_description">Breve descripción: <span>*</span></label>
                            <input type="text" class="form-control @error('short_description') is-invalid @enderror"
                                id="short_description" name="short_description"
                                value="{{ old('short_description', $job->short_description) }}"
                                placeholder="Descripción corta del empleo" required>
                            @error('short_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>


                        <div class="row">

                            {{-- Sub Título/area --}}
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="subtitle">Subtítulo/area: <span>*</span></label>
                                    <input type="text" class="form-control @error('subtitle') is-invalid @enderror"
                                        id="subtitle" name="subtitle"
                                        value="{{ old('subtitle', $job->subtitle) }}"
                                        placeholder="Subítulo/Area del empleo" required>
                                    @error('subtitle')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            {{-- Estado --}}
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="status_id">Estado: <span>*</span></label>
                                    <select class="form-control @error('status_id') is-invalid @enderror" 
                                            id="status_id" name="status_id" required>
                                        <option value="">Seleccione un estado</option>
                                        @foreach($statuses as $status)
                                            <option value="{{ $status->id }}" {{ old('status_id', $job->status_id) == $status->id ? 'selected' : '' }}>
                                                {{ $status->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('status_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        {{-- Imagen de portada --}}
                        <div class="form-group">
                            <label for="cover_image">Imagen de portada:</label>

                            {{-- Mostrar imagen actual si existe --}}
                            @if($job->cover_image)
                                <div class="mb-3">
                                    <p><strong>Imagen actual:</strong></p>
                                    <img src="{{ asset('storage/' . $job->cover_image) }}"
                                         alt="Imagen actual"
                                         class="img-thumbnail"
                                         style="max-width: 200px; max-height: 150px;">
                                    <p class="text-muted mt-2">Selecciona una nueva imagen para reemplazar la actual</p>
                                </div>
                            @endif

                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" id="cover_image">Nueva imagen</span>
                                </div>
                                <div class="custom-file">
                                    <input type="file" name="cover_image"
                                        class="custom-file-input @error('cover_image') is-invalid @enderror"
                                        id="inputGroupFile01" aria-describedby="inputGroupFileAddon01">
                                    <label class="custom-file-label" for="inputGroupFile01">
                                        {{ $job->cover_image ? 'Cambiar imagen' : 'Buscar imagen' }}
                                    </label>
                                    @error('cover_image')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <p class="text-muted">Formatos permitidos: jpeg, png, jpg, gif y webp. Opcional: deja vacío para mantener la imagen actual.</p>
                        </div>

                        {{-- Contenido --}}
                        <div class="form-group">
                            <label for="description">Detalles del empleo/puesto: <span>*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description"
                                      name="description"
                                      placeholder="Detalles del puesto/empleo"
                                      rows="10"
                                      required>{{ old('description', $job->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">
                                Describe las responsabilidades, requisitos y beneficios del puesto.
                            </small>
                        </div>

                        {{-- Botones de acción --}}
                        <div class="row">
                            <div class="col-md-12">
                                <button class="btn btn-success btn-block" type="submit">
                                    <i class="fas fa-save"></i> Actualizar empleo
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
    <style>
        .form-group label span {
            color: #dc3545;
            font-weight: bold;
        }

        .img-thumbnail {
            border: 2px solid #dee2e6;
            border-radius: 8px;
        }

        .card-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            border: none;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #218838 0%, #1c7430 100%);
            transform: translateY(-1px);
        }

        .custom-file-label::after {
            content: "Buscar";
        }
    </style>
@stop

@section('js')
    <script>
        tinymce.init({
            selector: 'textarea',
            plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
            toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat',
            language: 'es',
            height: 400
        });
    </script>
    <script>
        $(document).ready(function() {
            // Actualizar el nombre del archivo seleccionado
            $('.custom-file-input').on('change', function() {
                let fileName = $(this).val().split('\\').pop();
                $(this).siblings('.custom-file-label').addClass("selected").html(fileName || 'Buscar imagen');
            });

            // Confirmación antes de enviar el formulario
            $('form').on('submit', function(e) {
                if (!confirm('¿Estás seguro de que deseas actualizar este empleo?')) {
                    e.preventDefault();
                    return false;
                }
            });

            // Auto-resize del textarea
            $('#description').on('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });

            // Validación en tiempo real
            $('input[required], textarea[required], select[required]').on('blur', function() {
                if ($(this).val().trim() === '') {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                }
            });

            // Limpiar validación al escribir
            $('input, textarea, select').on('input change', function() {
                $(this).removeClass('is-invalid is-valid');
            });
        });
    </script>
@stop