@extends('adminlte::page')

@section('title', 'Mensaje<PERSON>')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1><PERSON><PERSON><PERSON>s</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ url()->previous() }}">Mensajes</a></li>
                    <li class="breadcrumb-item active">{{ $viewMessage->subject }}</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
<div class="row">
    <div class="col-12 col-sm-6 col-md-4">
        <div class="info-box">
            <span class="info-box-icon bg-info elevation-1">
                <i class="fas fa-envelope"></i>
            </span>
            <div class="info-box-content">
                <span class="info-box-text">Correo electrónico:</span>
                <span class="info-box-number">
                    {{ $viewMessage->email }}
                </span>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-md-4">
        <div class="info-box">
            <span class="info-box-icon bg-info elevation-1">
                <i class="fas fa-user"></i>
            </span>
            <div class="info-box-content">
                <span class="info-box-text">Enviado por:</span>
                <span class="info-box-number">
                    {{ $viewMessage->full_name }}
                </span>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-md-4">
        <div class="info-box">
            <span class="info-box-icon bg-info elevation-1">
                <i class="fas fa-inbox"></i>
            </span>
            <div class="info-box-content">
                <span class="info-box-text">Asunto:</span>
                <span class="info-box-number">
                    {{ $viewMessage->subject }}
                </span>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <p>{{ $viewMessage->message }}</p>
            </div>
            <div class="card-footer">
                <form action="{{ route('admin.delete.message', $viewMessage->id) }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger btn-block">Eliminar</button>
                </form>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
@stop

@section('js')
@stop