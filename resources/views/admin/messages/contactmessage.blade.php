@extends('adminlte::page')

@section('title', 'Mensaje<PERSON>')

@section('content_header')
    <h1><PERSON><PERSON><PERSON><PERSON></h1>
@stop

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card card-outline card-primary">
            <div class="card-header">
                <h3 class="card-title">Listado de mensajes</h3>
            </div>
            <div class="card-body table-responsive">
                <table class="table table-head-fixed text-nowrap table-hover table-responsive-lg" id="tablaMensajes">
                    <thead>
                        <tr>
                            <th>Nombres</th>
                            <th>Asuntos</th>
                            <th>Correos electrónicos</th>
                            <th>Teléfonos</th>
                            <th>Leido</th>
                            <th>Fecha de envío</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($contactMessages as $message)
                            <tr>
                                <td>{{ $message->full_name }}</td>
                                <td>{{ $message->subject }}</td>
                                <td>{{ $message->email }}</td>
                                <td>{{ $message->phone }}</td>
                                <td>
                                    @if ($message->read)
                                        <span class="badge badge-secondary">Mensaje leído</span>
                                    @else
                                        <span class="badge badge-primary">Nuevo</span>
                                    @endif
                                </td>
                                <td>{{ $message->created_at }}</td>
                                <td align="center">
                                    <a href="{{ route('admin.view.message', $message->id) }}">
                                        <i class="fas fa-eye"> Ver mensaje</i>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
<style>
    table th, table td {
        vertical-align: middle !important;
    }
</style>
@stop

@section('js')
<script>
    $(document).ready(function() {
        $('#tablaMensajes').DataTable({
            language: {
                "search": "Buscar",
                "zeroRecords": "No se encontraron elementos coincidentes",
                "lengthMenu": "Mostrar _MENU_ elementos",
                "loadingRecords": "Cargando...",
                "processing": "Procesando...",
                "info": "Mostrando del _START_ al _END_ de _TOTAL_ elementos",
                "infoEmpty": "Mostrando del 0 al 0 de 0 elementos",
                "infoFiltered": "(filtrados de _MAX_ elementos totales)",
                "paginate": {
                    "previous": '<i class="fas fa-chevron-left"></i>',
                    "next": '<i class="fas fa-chevron-right"></i>',
                    "first": "Inicio",
                    "last": "Fin"
                }
            },
            "lengthMenu": [[20,30,50, -1], [20,30,50, "Todo"]],
            // --- ADDED THIS LINE FOR DEFAULT SORTING ---
            "order": [[ 5, "desc" ]] // Sort by the 6th column (index 5) in descending order
        });
    });
</script>
@stop