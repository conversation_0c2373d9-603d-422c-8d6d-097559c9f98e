@extends('adminlte::page')

@section('title', 'Crear usuario')

@section('content_header')
    <h1>Crear usuario</h1>
@stop

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Información del usuario</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.user.register') }}" method="POST">
                        @csrf
                        <div class="form-group">
                            <label for="name">Nombres: <span>*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                id="name" name="name"     
                                value="{{ old('name') }}" 
                                placeholder="Nombres" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="last_name">Apellidos: <span>*</span></label>
                            <input type="text" class="form-control @error('last_name') is-invalid @enderror" 
                                id="last_name" name="last_name"     
                                value="{{ old('last_name') }}" 
                                placeholder="Apellidos" required>
                            @error('last_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="email">Email: <span>*</span></label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                id="email" name="email"     
                                value="{{ old('email') }}" 
                                placeholder="Email" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        {{-- rol de usuario --}}
                        <div class="form-group">
                            <label for="role">Rol: <span>*</span></label>
                            <select class="form-control @error('role') is-invalid @enderror" id="role" name="role" required>
                                <option value="">Seleccione un rol</option>
                                <option value="Administrador" {{ old('role') == 'Administrador' ? 'selected' : '' }}>Administrador</option>
                                <option value="Participante" {{ old('role') == 'Participante' ? 'selected' : '' }}>Participante</option>
                            </select>
                            @error('role')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <button type="submit" class="btn btn-primary btn-block">Crear usuario</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
@stop

@section('js')
@stop