@extends('adminlte::page')

@section('title', 'Usuarios')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <h1>Usuarios</h1>
        <a href="{{ route('admin.user.create') }}" class="btn btn-primary">Crear usuario</a>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Usuarios registrados</h3>
                </div>
                <div class="card-body">
                    <table class="table table-fixed table-responsive-lg table-hover" id="tablaUsuarios">
                        <thead>
                            <tr>
                                <th>Nombres</th>
                                <th>Apellidos</th>
                                <th>Email</th>
                                <th>Roles</th>
                                <th>Verificado</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($users as $user)
                                <tr>
                                    <td>{{ $user->name }}</td>
                                    <td>{{ $user->last_name}}</td>
                                    <td>{{ $user->email }}</td>
                                    {{-- <td>{{$user->roles->first()->name}}</td> --}}
                                    @php
                                        $roleBadges = [
                                            'Super Admin' => 'badge-success',
                                            'Administrador' => 'badge-success',
                                            'Usuario bloqueado' => 'badge-danger',
                                            'Participante' => 'badge-primary',
                                        ];
                                    @endphp
                                    <td>
                                        @foreach ($roleBadges as $roleName => $badgeClass)
                                            @if ($user->hasRole($roleName))
                                                <span class="badge {{ $badgeClass }}">{{ $roleName }}</span>
                                            @endif
                                        @endforeach
                                    </td>
                                    <td>
                                        @if ($user->email_verified_at !== null)
                                            <span class="badge badge-success">Verficado</span>
                                        @endif
                                        @if ($user->email_verified_at == null)
                                            <span class="badge badge-danger">Bloqueado/No verificado</span>
                                        @endif
                                    </td>
                                    <td> 
                                        <a href="{{ route('admin.user.edit', $user->id) }}" class="btn btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>    
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
@stop

@section('js')
<script>
    $(document).ready(function() {
        $('#tablaUsuarios').DataTable( {
            language: {
                "search": "Buscar",
                "zeroRecords": "No se encontraron elementos coincidentes",
                "lengthMenu": "Mostrar _MENU_ elementos",
                "loadingRecords": "Cargando...",
                "processing": "Procesando...",
                "info": "Mostrando del _START_ al _END_ de _TOTAL_ elementos",
                "infoEmpty": "Mostrando del 0 al 0 de 0 elementos",
                "infoFiltered": "(filtrados de _MAX_ elementos totales)",
                "paginate": {
                    "previous": '<i class="fas fa-chevron-left"></i>',
                    "next": '<i class="fas fa-chevron-right"></i>',
                    "first": "Inicio",
                    "last": "Fin"
                }
            },
            "lengthMenu": [[20,30,50, -1], [20,30,50, "Todo"]],
            dom: 'lfrtip',
            buttons: [
                'copy',
                'csv',
                'excel',
                'pdf',
            ]
        });
    });
</script>
@stop