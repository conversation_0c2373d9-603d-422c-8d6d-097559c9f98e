@extends('adminlte::page')

@section('title', 'Usuarios')

@section('content_header')
    <h1>Usuarios</h1>
@stop

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">USUARIOS REGISTRADOS</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.user.update', $user->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="form-group">
                            <label for="name">Nombres: <span>*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                id="name" name="name"     
                                value="{{ old('name', $user->name) }}" 
                                placeholder="Nombres" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="last_name">Apellidos: <span>*</span></label>
                            <input type="text" class="form-control @error('last_name') is-invalid @enderror" 
                                id="last_name" name="last_name"     
                                value="{{ old('last_name', $user->last_name) }}" 
                                placeholder="Apellidos" required>
                            @error('last_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="email">Email: <span>*</span></label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                id="email" name="email"     
                                value="{{ old('email', $user->email) }}" 
                                placeholder="Email" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <button type="submit" class="btn btn-primary btn-block">Actualizar</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Acciones</h3>
                </div>
                <div class="card-body">
                    <a href="{{ route('admin.user.send.email.new.password', $user->id) }}" class="btn btn-warning">Enviar correo con nueva contraseña temporal</a>
                    @if ($user->email_verified_at == null)
                        <a href="{{ route('admin.user.unblock', $user->id) }}" class="btn btn-success">Desbloquear usuario</a>
                    @else
                        <a href="{{ route('admin.user.block', $user->id) }}" class="btn btn-danger">Bloquear usuario</a>
                    @endif
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
@stop

@section('js')
@stop