@extends('adminlte::page')

@section('title', 'Editar perfil')

@section('content_header')
    <h1>Editar mi perfil</h1>
@stop

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Actualizar contraseña</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.user.update.password') }}" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="form-group">
                            <label for="current_password">Contraseña actual: <span>*</span></label>
                            <input type="password" class="form-control @error('current_password') is-invalid @enderror" 
                                id="current_password" name="current_password"     
                                value="{{ old('current_password') }}" 
                                placeholder="Contraseña actual" required>
                            @error('current_password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="password">Nueva contraseña: <span>*</span></label>
                            <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                id="password" name="password"     
                                value="{{ old('password') }}" 
                                placeholder="Nueva contraseña" required>
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="password_confirmation">Confirmar contraseña: <span>*</span></label>
                            <input type="password" class="form-control @error('password_confirmation') is-invalid @enderror" 
                                id="password_confirmation" name="password_confirmation"     
                                value="{{ old('password_confirmation') }}" 
                                placeholder="Confirmar contraseña" required>
                            @error('password_confirmation')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <button type="submit" class="btn btn-primary btn-block">Actualizar</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

@stop

@section('css')
@stop

@section('js')
@stop