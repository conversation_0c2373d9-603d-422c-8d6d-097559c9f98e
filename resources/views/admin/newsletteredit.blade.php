@extends('adminlte::page')

@section('title', 'Suscripciones')

@section('content_header')
    <h1>Editar suscriptor</h1>
@stop

@section('subtitle', 'Welcome')
@section('content_header_title', 'Home')
@section('content_header_subtitle', 'Welcome')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {{-- <form action="{{ route('admin.subscribers.update', $newsletterSubscriber->id) }}" method="POST"> --}}
                <form method="POST">
                    @csrf
                    <div class="form-group">
                        <label for="exampleInputEmail1">Nombre del suscriptor:</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" placeholder="Nombre del suscriptor" value="{{ $newsletterSubscriber->full_name }}">
                    </div>
                    <div class="form-group">
                        <label for="exampleInputEmail1">Email del suscriptor:</label>
                        <input type="email" class="form-control" id="email" name="email" placeholder="Email del suscriptor" value="{{ $newsletterSubscriber->email }}">
                    </div>
                    <div class="form-group">
                        <label for="exampleInputEmail1">Empresa del suscriptor:</label>
                        <input type="text" class="form-control" id="company" name="company" placeholder="Empresa del suscriptor" value="{{ $newsletterSubscriber->company }}">
                    </div>
                    <div class="form-group">
                        <label for="exampleInputEmail1">Pais del suscriptor</label>
                        <input type="text" class="form-control" id="country" name="country" placeholder="Pais del suscriptor" value="{{ $newsletterSubscriber->country }}">
                    </div>
                    <button type="submit" class="btn btn-primary">Actualizar</button>
                </form>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
@stop

@section('js')
@stop