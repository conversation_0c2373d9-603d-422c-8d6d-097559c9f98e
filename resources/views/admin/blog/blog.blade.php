@extends('adminlte::page')

@section('title', 'Blog')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <h1>Blog</h1>
        <a href="{{ route('admin.view.new.post') }}" class="btn btn-primary">Nueva publicación</a>
    </div>
@stop

@section('content')
{{-- <div class="row">
    <div class="col-12 col-sm-6 col-md-6">
        <div class="info-box">
            <span class="info-box-icon bg-success elevation-1">
                <i class="fas fa-layer-group"></i>
            </span>
            <div class="info-box-content">
                <span class="info-box-text">Categorias</span>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-md-6">
        <div class="info-box">
            <span class="info-box-icon bg-warning elevation-1">
                <i class="fas fa-tags"></i>
            </span>
            <div class="info-box-content">
                <span class="info-box-text">Etiquetas</span>
            </div>
        </div>
    </div>
</div> --}}
<div class="row">
    <div class="col-12 col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Listado de publicaciones</h3>
            </div>
            <div class="card-body table-responsive">
                <table class="table table-head-fixed text-nowrap table-responsive-lg table-hover table-bordered" id="tablaPublicaciones">
                    <thead>
                        <tr>
                            <th></th>
                            <th>Titulo</th>
                            <th>Fecha de publicación</th>
                            <th>Estado</th>
                            <th>Fecha de creación</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($blogPosts as $post)
                            <tr>
                                <td>
                                    <img src="{{ Storage::url($post->image_path) }}" alt="{{ $post->title }}" width="50px">
                                </td>
                                <td>{{ Str::limit($post->title, 50, '...') }}</td>
                                <td>{{ $post->published_at }}</td>
                                <td align="center">
                                    <span class="badge badge-{{ $post->status->name == 'En línea' ? 'success' : 'warning' }}">{{ $post->status->name }}</span>
                                </td>
                                <td>{{ $post->created_at }}</td>
                                <td><a class="btn btn-warning" href="{{ route('admin.blog.view.post', $post->id) }}"><i class="fas fa-edit"></i></a></td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
<style>
    table th, table td {
        vertical-align: middle !important;
    }
</style>
@stop

@section('js')
<script>
    $(document).ready(function() {
        $('#tablaPublicaciones').DataTable( {
            language: {
                "search": "Buscar",
                "zeroRecords": "No se encontraron elementos coincidentes",
                "lengthMenu": "Mostrar _MENU_ elementos",
                "loadingRecords": "Cargando...",
                "processing": "Procesando...",
                "info": "Mostrando del _START_ al _END_ de _TOTAL_ elementos",
                "infoEmpty": "Mostrando del 0 al 0 de 0 elementos",
                "infoFiltered": "(filtrados de _MAX_ elementos totales)",
                "paginate": {
                    "previous": '<i class="fas fa-chevron-left"></i>',
                    "next": '<i class="fas fa-chevron-right"></i>',
                    "first": "Inicio",
                    "last": "Fin"
                }
            },
            "lengthMenu": [[20,30,50, -1], [20,30,50, "Todo"]],
            dom: 'lfrtip',
            buttons: [
                'copy',
                'csv',
                'excel',
                'pdf',
            ]
        });
    });
</script>
@stop