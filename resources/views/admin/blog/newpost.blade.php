@extends('adminlte::page')

@section('title', 'Blog')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Nueva publicación</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('admin.blog') }}">Blog</a></li>
                    <li class="breadcrumb-item active">Nueva publicación</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card card-outline card-primary">
                <div class="card-header">
                    <h3 class="card-title">Nueva publicación</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('posts.store') }}" enctype="multipart/form-data">
                        @csrf
                        {{-- Título --}}
                        <div class="form-group">
                            <label for="title">Título: <span>*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                id="title" name="title" 
                                value="{{ old('title') }}" 
                                placeholder="Título de la publicación" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        {{-- Imagen de portada --}}
                        <div class="form-group">
                            <label for="cover_image">Imagen de portada: <span>*</span></label>
                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" id="cover_image">Imagen de portada</span>
                                </div>
                                <div class="custom-file">
                                    <input type="file" name="cover_image" 
                                        class="custom-file-input @error('cover_image') is-invalid @enderror" 
                                        id="inputGroupFile01" aria-describedby="inputGroupFileAddon01">
                                    <label class="custom-file-label" for="inputGroupFile01">Buscar imagen</label>
                                    @error('cover_image')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <p>Formatos permitidos: jpeg, png, jpg, gif y webp.</p>
                        </div>

                        <div class="row">
                            {{-- Fecha --}}
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="published_at">Fecha: <span>*</span></label>
                                    <input type="date" class="form-control @error('published_at') is-invalid @enderror" id="published_at" name="published_at" value="{{ old('published_at') }}" required>
                                    @error('published_at')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            {{-- Categoría --}}
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="category_id">Categoría: <span>*</span></label>
                                    <select class="form-control @error('category_id') is-invalid @enderror" 
                                            id="category_id" name="category_id" required>
                                        <option value="">Seleccione una categoría</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            {{-- Estado --}}
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="status_id">Estado: <span>*</span></label>
                                    <select class="form-control @error('status_id') is-invalid @enderror" 
                                            id="status_id" name="status_id" required>
                                        <option value="">Seleccione un estado</option>
                                        @foreach($statuses as $status)
                                            <option value="{{ $status->id }}" {{ old('status_id') == $status->id ? 'selected' : '' }}>
                                                {{ $status->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('status_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        {{-- Contenido --}}
                        <div class="form-group">
                            <label for="content">Contenido: <span>*</span></label>
                            <textarea class="form-control @error('content') is-invalid @enderror" id="content" name="content" placeholder="Contenido de la publicación" rows="10">{{ old('content') }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <br>
                        <button class="btn btn-primary btn-block" type="submit">Publicar</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')

@stop

@section('js')
<script>
    tinymce.init({
        selector: 'textarea',
        plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
        toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat',
        language: 'es',
        height: 400
    });
</script>
@stop