@extends('adminlte::page')

@section('title', 'Blog')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Nueva publicación</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('admin.blog') }}">Blog</a></li> {{-- Changed to use route() --}}
                    <li class="breadcrumb-item active">Nueva publicación</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Nueva publicación</h3>
                </div>
                <div class="card-body">
                    {{-- Important: Give your main form an ID --}}
                    {{-- Use the named route for the form action --}}
                    <form method="POST" action="{{ route('posts.store') }}" id="blogPostForm" enctype="multipart/form-data">
                        @csrf
                        <div class="form-group">
                            <label for="title">Título:</label>
                            <input type="text" class="form-control" id="title" name="title" placeholder="Título de la publicación" required>
                        </div>

                        <div class="form-group">
                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" id="inputGroupFileAddon01">Imagen de portada</span>
                                </div>
                                <div class="custom-file">
                                    <input type="file" class="custom-file-input" id="inputGroupFile01" aria-describedby="inputGroupFileAddon01">
                                    <label class="custom-file-label" for="inputGroupFile01">Buscar imagen</label>
                                </div>
                            </div>
                        </div>

                        {{-- Dropzone Area --}}
                        <div class="form-group">
                            <label for="coverImageDropzone">Imagen de portada:</label>
                            <div id="coverImageDropzone" class="dropzone"></div>
                            <input type="hidden" name="cover_image_path" id="cover_image_path">
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="published_at">Fecha:</label>
                                    <input type="date" class="form-control" id="published_at" name="published_at" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="category_id">Categoría:</label>
                                    <select class="form-control" id="category_id" name="category_id" required>
                                        <option value="">Seleccione una categoría</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}">{{ $category->name }}</option>
                                        @endforeach
                                    </select>
                                   
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="status_id">Estado:</label>
                                    <select class="form-control" id="status_id" name="status_id" required>
                                        <option value="">Seleccione un estado</option>
                                        @foreach($statuses as $status)
                                            <option value="{{ $status->id }}">{{ $status->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="content">Contenido:</label>
                            <textarea class="form-control" id="content" name="content" placeholder="Contenido de la publicación" rows="5" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="card-footer" style="display: flex; justify-content: flex-end;">
                    <button type="button" class="btn btn-primary btn-block" id="submitAllForm">Guardar publicación</button>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.css">
    <style>
        .dropzone {
            border: 2px dashed #007bff;
            border-radius: 5px;
            background: #f8f9fa;
            padding: 20px;
            min-height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-family: Arial, sans-serif;
            color: #6c757d;
        }
        .dropzone .dz-message {
            margin: 0;
            font-size: 1.2em;
        }
        .dropzone .dz-preview .dz-image img {
            width: 100px;
            height: 100px;
            object-fit: cover;
        }
    </style>
@stop

@section('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.js"></script>
    <script>
        Dropzone.autoDiscover = false;

        $(document).ready(function() {
            var myDropzone;

            // Initialize Dropzone with your temporary upload route
            myDropzone = new Dropzone("#coverImageDropzone", {
                url: "{{ route('image.upload.temp') }}", // Use Laravel's named route helper
                paramName: "image",
                maxFilesize: 5,
                acceptedFiles: "image/*",
                addRemoveLinks: true,
                dictDefaultMessage: "Arrastra tu imagen aquí o haz clic para subirla.",
                dictRemoveFile: "Quitar imagen",
                maxFiles: 1,
                autoProcessQueue: false, // Essential for manual upload
                uploadMultiple: false, // Ensure only one file is uploaded at a time
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                init: function() {
                    var dropzoneInstance = this;

                    this.on("addedfile", function(file) {
                        console.log("File added:", file.name);
                        // If more than one file is added, remove the previous one
                        if (this.files.length > 1) {
                            this.removeFile(this.files[0]);
                        }
                        // Clear any previous image path from the hidden input
                        $('#cover_image_path').val('');
                    });

                    this.on("removedfile", function(file) {
                        console.log("File removed:", file.name);
                        $('#cover_image_path').val(''); // Clear hidden input on removal
                    });

                    this.on("queuecomplete", function() {
                        // This event fires after processQueue is done (all files uploaded)
                        console.log("Dropzone queue complete.");
                        // Only submit the main form if all Dropzone processing is truly finished
                        if (dropzoneInstance.getQueuedFiles().length === 0 && dropzoneInstance.getUploadingFiles().length === 0) {
                            $('#blogPostForm').off('submit').submit(); // Submit the main form
                        }
                    });

                    this.on("success", function(file, response) {
                        console.log("Temporary upload success:", response);
                        // Store the temporary path returned by your controller
                        $('#cover_image_path').val(response.path);
                    });

                    this.on("error", function(file, message) {
                        console.error("Dropzone upload error:", message);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error al subir la imagen',
                            text: message,
                            confirmButtonText: 'Ok'
                        });
                        // Re-enable the submit button
                        $('#submitAllForm').prop('disabled', false).text('Guardar publicación');
                    });
                }
            });

            // Handle the main form submission when the "Guardar" button is clicked
            $('#submitAllForm').on('click', function(e) {
                e.preventDefault(); // Prevent default form submission

                // Basic form validation before proceeding
                // if (!$('#title').val() || !$('#content').val() || !$('#published_at').val() || !$('#category_id').val() || !$('#status_id').val()) {
                //     Swal.fire({
                //         icon: 'warning',
                //         title: 'Campos requeridos',
                //         text: 'Por favor, rellene todos los campos obligatorios.',
                //         confirmButtonText: 'Entendido'
                //     });
                //     return; // Stop execution if validation fails
                // }

                $(this).prop('disabled', true).text('Guardando...'); // Disable button and change text

                // Check if there's a file in the Dropzone queue
                if (myDropzone.getQueuedFiles().length > 0) {
                    // If there's a file, process (upload) it first
                    myDropzone.processQueue();
                } else {
                    // No file to upload, just submit the main form directly
                    $('#blogPostForm').submit();
                }
            });
            $('#blogPostForm').on('submit', function(e) {
                if ($('#submitAllForm').is(':enabled')) {
                    e.preventDefault(); 
                    $('#submitAllForm').click(); // Manually trigger our button click handler
                }
            });
        });
    </script>
@stop