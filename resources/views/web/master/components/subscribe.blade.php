<!-- Contact Start -->
<div class="container-fluid contact bg-light py-5">
    <div class="container py-5">
        <div class="text-center mx-auto pb-5 wow fadeInUp" data-wow-delay="0.2s" style="max-width: 800px;">
            <h4 class="text-primary">Boletín</h4>
            <h1 class="display-4 mb-4">
                Suscríbete a nuestro boletín electrónico
            </h1>
        </div>
        <div class="row g-5">
            <div class="col-xl-6 wow fadeInLeft" data-wow-delay="0.2s">
                <div class="contact-img d-flex justify-content-center" >
                    <div class="contact-img-inner"  style="width: 400px;">
                        <img src="{{ asset('img/subscribe.svg') }}"  class="img-fluid w-100"  alt="subscribe avance y desempeño">
                    </div>
                </div>
            </div>
            <div class="col-xl-6 wow fadeInRight" data-wow-delay="0.4s">
                <div>
                    <p class="mb-4">
                        Suscríbete para recibir cada semana contenido exclusivo: tips aplicables, herramientas descargables y videos didácticos que te guiarán en la gestión estratégica del talento humano. Impulsa el desempeño, el compromiso y la productividad en tu empresa con conocimientos de vanguardia.
                    </p>
                    <form action="{{ route('newsletter.subscriber') }}" method="POST">
                        @csrf
                        <div class="row g-3">
                            <div class="col-lg-12 col-xl-6">
                                <div class="form-floating">
                                    <div style="display:none">
                                        <input type="text" name="website" value="">
                                    </div>
                                    <input type="text" class="form-control border-0 @error('full_name') is-invalid @enderror" id="full_name" name="full_name"
                                        placeholder="Tu nombre y apellido" value="{{ old('full_name') }}" pattern="[A-Za-z ]+" oninput="this.value = this.value.replace(/[^a-zA-ZñÑáéíóúÁÉÍÓÚ\s]/g, '');" required>
                                    <label for="full_name">Nombre y apellido *</label>
                                    @error('full_name')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-lg-12 col-xl-6">
                                <div class="form-floating">
                                    <input type="email" class="form-control border-0 @error('email') is-invalid @enderror" id="email" name="email"
                                        placeholder="Your Email" value="{{ old('email') }}" pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$">
                                    <label for="email">Correo electrónico *</label>
                                    @error('email')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-lg-12 col-xl-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control border-0 @error('company') is-invalid @enderror" id="company" name="company"
                                        placeholder="Empresa" value="{{ old('company') }}">
                                    <label for="company">Empresa</label>
                                    @error('company')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-lg-12 col-xl-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control border-0 @error('country') is-invalid @enderror" id="country" name="country"
                                        placeholder="Pais" value="{{ old('country') }}" pattern="[A-Za-z ]+" oninput="this.value = this.value.replace(/[^a-zA-ZñÑáéíóúÁÉÍÓÚ\s]/g, '');" required>
                                    <label for="country">País*</label>
                                    @error('country')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-12">
                                <button class="btn btn-primary w-100 py-3" type="submit">Suscribirme</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Contact End -->
