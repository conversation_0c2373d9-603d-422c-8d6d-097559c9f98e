<div class="container-fluid bg-breadcrumb" style="background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url({{ asset('img/header/header1.jpg') }});">
    <div class="bg-breadcrumb-single"></div>
    <div class="container text-center py-5" style="max-width: 900px;">
        @php
            $pageTitles = [
                'nosotros' => ['title' => 'Acerca de Nosotros', 'breadcrumb' => 'Nosotros'],
                'contactanos' => ['title' => 'Contáctanos', 'breadcrumb' => 'Contáctanos'],
                'blog' => ['title' => 'Nuestro Blog', 'breadcrumb' => 'Blog'],
                'consultorias' => ['title' => 'Consultorías', 'breadcrumb' => 'Consultorías'],
                'consultorias-opc2' => ['title' => 'Consultorías', 'breadcrumb' => 'Consultorías'],
                'consultorias-opc3' => ['title' => 'Consultorías', 'breadcrumb' => 'Consultorías'],
                'consultorias-opc4' => ['title' => 'Consultorías', 'breadcrumb' => 'Consultorías'],
                'formacion-profesional' => ['title' => 'Formación profesional', 'breadcrumb' => 'Formación profesional'],
                'bpos' => ['title' => "BPO's/Reclutamiento", 'breadcrumb' => "BPO's/Reclutamiento"],
                'servicios' => ['title' => 'Nuestros Servicios', 'breadcrumb' => 'Servicios'],
            ];
            $currentRouteName = request()->route()->getName();
            $defaultTitle = 'Avance y Desempeño';
            $defaultBreadcrumb = 'Home';
            $pageData = $pageTitles[$currentRouteName] ?? ['title' => $defaultTitle, 'breadcrumb' => $defaultBreadcrumb];
        @endphp

        <h4 class="text-white display-4 mb-4 wow fadeInDown" data-wow-delay="0.1s">
            {{ $pageData['title'] }}
        </h4>
        <ol class="breadcrumb justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
            @if (request()->routeIs('blog.detail'))
                <li class="breadcrumb-item" style="color: #fff">DETALLES DE NUESTRO BLOG</li>
            @else
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Inicio</a></li>
                <li class="breadcrumb-item active text-white">
                    {{ $pageData['breadcrumb'] }}
                </li>
            @endif
        </ol>    
    </div>
</div>