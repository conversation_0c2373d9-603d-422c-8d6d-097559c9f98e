<div class="container-fluid feature gray-background py-5">
    <div class="container py-5">
        <div class="text-center mx-auto pb-5 wow fadeInUp" data-wow-delay="0.2s" style="max-width: 800px;">
            <h4 class="text-primary">{{ $clientes->sub_title }}</h4>
            <h1 class="display-4 mb-4">{{ $clientes->title }}</h1>
            <p class="mb-0" style="color: #000">
                {{ $clientes->content }}
                {{-- Trabajamos con organizaciones líderes que no solo buscan la excelencia, sino que la construyen activamente a través del desarrollo estratégico de su talento humano. Son empresas que invierten en su activo más valioso, logrando resultados extraordinarios y forjando el futuro de su sector con nuestra ayuda. --}}
            </p>
        </div>
        
        <!-- Customers Carousel -->
        <div class="customers-carousel-container wow fadeInUp" data-wow-delay="0.1s">
            <div class="customers-carousel owl-carousel">
                @foreach($clientesImagesRow1 as $imagen)
                    <div class="customer-logo">
                        <img src="{{ asset($imagen->image) }}" class="img-fluid" alt="{{ $imagen->alt }}">
                    </div>
                @endforeach
            </div>
        </div>

        <br>

        <div class="customers-carousel-container wow fadeInUp" data-wow-delay="0.2s">
            <div class="customers-carousel owl-carousel">
                @foreach($clientesImagesRow2 as $imagen)
                    <div class="customer-logo">
                        <img src="{{ asset($imagen->image) }}" class="img-fluid" alt="{{ $imagen->alt }}">
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>

<style>
.customers-carousel-container {
    /* background: white; */
    background: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    padding: 25px 30px;
}

.customer-logo {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 3px solid transparent;
    margin: 8px 5px;
}

.customer-logo:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(23, 56, 90, 0.2);
    border-color: var(--bs-primary);
}

.customer-logo img {
    max-height: 100px;
    max-width: 180px;
    object-fit: contain;
    filter: grayscale(100%);
    transition: all 0.3s ease;
}

.customer-logo:hover img {
    filter: grayscale(0%);
    transform: scale(1.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .customers-carousel-container {
        padding: 20px;
    }
    
    .customer-logo {
        height: 120px;
        padding: 15px;
        margin: 6px 3px;
    }
    
    .customer-logo img {
        max-height: 85px;
        max-width: 150px;
    }
}

@media (max-width: 576px) {
    .customers-carousel-container {
        padding: 15px;
    }
    
    .customer-logo {
        height: 100px;
        padding: 12px;
        margin: 4px 2px;
    }
    
    .customer-logo img {
        max-height: 70px;
        max-width: 120px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize customers carousel
    if (typeof $ !== 'undefined' && $.fn.owlCarousel) {
        const carousel = $('.customers-carousel').owlCarousel({
            items: 5,
            loop: true,
            margin: 20,
            nav: false,
            dots: false,
            autoplay: true,
            autoplayTimeout: 3000,
            autoplayHoverPause: true,
            mouseDrag: true,
            touchDrag: true,
            smartSpeed: 800,
            responsive: {
                0: {
                    items: 1
                },
                576: {
                    items: 2
                },
                768: {
                    items: 3
                },
                992: {
                    items: 4
                },
                1200: {
                    items: 5
                }
            }
        });

        // Asegurar que el autoplay se reanude después del hover
        $('.customer-logo').on('mouseenter', function() {
            carousel.trigger('stop.owl.autoplay');
        });

        $('.customer-logo').on('mouseleave', function() {
            carousel.trigger('play.owl.autoplay', [3000]);
        });

        // También para el contenedor completo
        $('.customers-carousel-container').on('mouseleave', function() {
            carousel.trigger('play.owl.autoplay', [3000]);
        });

        // Mantener el carrusel funcionando aunque la página no esté visible
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // Página oculta - mantener autoplay
                carousel.trigger('play.owl.autoplay', [3000]);
            } else {
                // Página visible - asegurar que siga funcionando
                carousel.trigger('play.owl.autoplay', [3000]);
            }
        });

        // Reanudar autoplay cuando la ventana recupera el foco
        window.addEventListener('focus', function() {
            carousel.trigger('play.owl.autoplay', [3000]);
        });

        // Forzar que el carrusel nunca se detenga completamente
        setInterval(function() {
            if (!carousel.hasClass('owl-drag') && !$('.customer-logo:hover').length) {
                carousel.trigger('play.owl.autoplay', [3000]);
            }
        }, 5000);

        // Soporte para mousepad/trackpad de laptop (wheel events)
        let scrollTimeout;
        let isScrolling = false;
        
        $('.customers-carousel').on('wheel', function(e) {
            e.preventDefault();
            
            // Evitar múltiples triggers durante el scroll
            if (isScrolling) return;
            
            isScrolling = true;
            
            // Detectar dirección del scroll horizontal y vertical
            const deltaX = e.originalEvent.deltaX;
            const deltaY = e.originalEvent.deltaY;
            
            // Priorizar movimiento horizontal si existe, sino usar vertical
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                // Scroll horizontal
                if (deltaX > 0) {
                    // Scroll hacia la derecha = siguiente
                    carousel.trigger('next.owl.carousel');
                } else {
                    // Scroll hacia la izquierda = anterior
                    carousel.trigger('prev.owl.carousel');
                }
            } else {
                // Scroll vertical
                if (deltaY > 0) {
                    // Scroll hacia abajo = siguiente
                    carousel.trigger('next.owl.carousel');
                } else {
                    // Scroll hacia arriba = anterior
                    carousel.trigger('prev.owl.carousel');
                }
            }
            
            // Pausar autoplay temporalmente durante el scroll manual
            carousel.trigger('stop.owl.autoplay');
            
            // Limpiar timeout anterior si existe
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
            
            // Permitir siguiente scroll después de 300ms
            scrollTimeout = setTimeout(function() {
                isScrolling = false;
                // Reanudar autoplay después de 2 segundos de inactividad
                carousel.trigger('play.owl.autoplay', [3000]);
            }, 300);
        });
    }
});
</script>