<!-- Contact Start -->
<div class="container-fluid contact bg-light py-5">
    <div class="container py-5">
        <div class="row g-5">
            <div class="col-lg-6 wow fadeInLeft" data-wow-delay="0.1s">
                <div class="contact-item">
                    <div class="pb-5">
                        <h4 class="text-primary">Contáctanos</h4>
                        <h1 class="display-4 mb-4">Tu éxito, nuestro próximo proyecto. ¡Contáctanos!</h1>
                        <p><b>¿Listo para empezar tu proyecto? ¿Tienes preguntas o necesitas una cita?</b></p>
                        <p class="mb-0">
                            Usa el siguiente formulario para contactarnos. Proporciónanos tu nombre, número de teléfono, correo electrónico, el asunto de tu mensaje y cuéntanos sobre lo que necesitas. Te responderemos a la brevedad.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3s">
                <form action="{{ route('contact.message') }}" method="POST">
                    @csrf

                    {{-- Bloque para mostrar todos los errores de validación --}}
                    {{-- @if ($errors->any())
                        <div class="alert alert-danger mb-4">
                            <ul>
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif --}}

                    <div class="row g-3">
                        <div class="col-lg-12 col-xl-6">
                            <div class="form-floating">
                                <input type="text" class="form-control @error('full_name') is-invalid @enderror" id="full_name" name="full_name" pattern="[A-Za-z ]+" oninput="this.value = this.value.replace(/[^a-zA-ZñÑáéíóúÁÉÍÓÚ\s]/g, '');" placeholder="Nombre y apellido" 
                                value="{{ Auth::check() ? Auth::user()->name.' '.Auth::user()->last_name : old('full_name') }}" required>
                                <label for="full_name">Nombre y apellido *</label>
                                @error('full_name')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-12 col-xl-6">
                            <div class="form-floating">
                                <input type="text" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" inputmode="numeric" pattern="[0-9]{8}" maxlength="8" oninput="this.value = this.value.replace(/[^0-9]/g, '');" placeholder="Teléfono" 
                                value="{{ Auth::check() ? Auth::user()->phone : old('phone') }}">
                                <label for="phone">Teléfono</label>
                                @error('phone')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="form-floating">
                                <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" placeholder="Email" pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$" 
                                value="{{ Auth::check() ? Auth::user()->email : old('email') }}" required>
                                <label for="email">Correo electrónico *</label>
                                @error('email')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-floating">
                                <input type="text" class="form-control @error('subject') is-invalid @enderror" id="subject" name="subject" placeholder="Asunto" value="{{ old('subject') }}" required>
                                <label for="subject">Asunto *</label>
                                @error('subject')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-floating">
                                <textarea class="form-control @error('message') is-invalid @enderror" placeholder="Detalla tu mensaje" id="message" name="message" style="height: 160px" required>{{ old('message') }}</textarea>
                                <label for="message">Escribe tu mensaje *</label>
                                @error('message')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-12">
                            <button class="btn btn-primary w-100 py-3" type="submit">Enviar mensaje</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- Contact End -->
