@extends('web.master.master')

@section('title', 'Registro de usuario')

@section('cs')
    {{-- additional css --}}
@endsection

{{-- BODY --}}
{{-- CONTENT FOR DETAIL BLOG--}}
@section('content')
<div class="container-fluid blog pb-5">
        <br><br>
        <div class="container pb-5">
            <div class="text-center mx-auto pb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 800px;">
                <h1 class="display-4 mb-4">Registro de usuarios</h1>
                <p>
                    Crea una cuenta para acceder a nuestros servicios: 
                    postularte a los empleos que se encuentren disponibles en nuestro sitio web y también podrás realizar comentarios en nuestros blogs.
                </p>
            </div>
            <div class="text-center mx-auto pb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 800px;">
                <h4 class="text-primary">Primer paso: Verificaremos tu correo electrónico</h4>
                <p>
                    Te enviaremos un código de verificación que deberás ingresar en el formulario para poder crear tu cuenta.
                </p>
                <form method="POST" action="{{ route('validar-email') }}">
                    @csrf
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="email" class="form-label">Correo electrónico: <span class="text-danger">*</span></label>
                                <input
                                    type="email"
                                    class="form-control @error('email') is-invalid @enderror"
                                    id="email"
                                    name="email"
                                    value="{{ old('email') }}"
                                    aria-describedby="Correo electrónico"
                                >
                                @error('email')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                            <p>El código de verificación es único y válido por 15 minutos, si expira, puedes solicitar otro.</p>
                        </div>
                    </div>
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" type="submit"><i class="fas fa-envelope"></i> Enviar código de verificación</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

{{-- END CONTENT FOR DETAIL BLOG --}}

@section('js')
    {{-- additional js --}}
@endsection

{{-- END BODY --}}