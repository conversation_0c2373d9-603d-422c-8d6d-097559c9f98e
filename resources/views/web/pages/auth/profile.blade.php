@extends('web.master.master')

@section('title', 'Registro de usuario')

@section('cs')
    {{-- additional css --}}
@endsection

{{-- BODY --}}
{{-- CONTENT FOR DETAIL BLOG--}}
@section('content')

@if (Auth::check())
    <div class="container-fluid blog pb-5">
        <br><br>
        <div class="container pb-5">
            <div class="text-center mx-auto pb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 800px;">
                <h4 class="text-primary">Mi perfil</h4>
                <h1 class="display-4 mb-4">
                    @php
                        $hour = date('H'); // Obtiene la hora actual del servidor (en formato de 24 horas)
                        $greeting = '';

                        if ($hour >= 6 && $hour < 12) {
                            $greeting = "Buenos días";
                        } else if ($hour >= 12 && $hour < 18) {
                            $greeting = "Buenas tardes";
                        } else {
                            $greeting = "Buenas noches";
                        }
                    @endphp 
                    {{ $greeting }}, {{ Auth::user()->name }} {{ Auth::user()->last_name }}.
                </h1>
            </div>

            <div class=" wow fadeInUp" data-wow-delay="0.1s">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Empleos a los que he aplicados</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-head-fixed text-nowrap" id="tablaPostulantes">
                                    <thead>
                                        <tr>
                                            <th>Titulo</th>
                                            <th>Fecha de aplicación</th>
                                            <th>Estado</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($appliedEmployments as $employment)
                                            <tr>
                                                <td>{{ $employment->employment->title }}</td>
                                                <td>{{ $employment->created_at }}</td>
                                                <td>{{ $employment->status->name }}</td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="3" class="text-center">No has aplicado a ningún empleo aún.</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <br>
            <hr>
            <br>
            <div class=" wow fadeInUp" data-wow-delay="0.1s">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">Mi información para postular</h5>
                            </div>
                            <div class="card-body">
                                <p>Para aplicar a un empleo, debes tener completar la siguiente información:</p>
                                <form method="POST" action="{{ route('update-profile-general-user') }}" enctype="multipart/form-data">
                                    @csrf
                                    <div class="mb-3">
                                        <label for="phonenumber" class="form-label">Numero de celular: <span class="text-danger">*</span></label>
                                        <input
                                            type="text"
                                            class="form-control @error('phonenumber') is-invalid @enderror"
                                            id="phonenumber"
                                            name="phonenumber"
                                            value="{{ old('phonenumber', Auth::user()->participantProfile->phonenumber ?? '') }}"
                                            inputmode="numeric" pattern="[0-9]{8}" maxlength="8" oninput="this.value = this.value.replace(/[^0-9]/g, '');">
                                        @error('phonenumber')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                    <div class="mb-3">
                                        <label for="dui" class="form-label">DUI (sin guión): <span class="text-danger">*</span></label>
                                        <input
                                            type="text"
                                            class="form-control @error('dui') is-invalid @enderror"
                                            id="dui"
                                            name="dui"
                                            value="{{ old('dui', Auth::user()->participantProfile->dui ?? '') }}"
                                            inputmode="numeric" pattern="[0-9]{9}" maxlength="9" oninput="this.value = this.value.replace(/[^0-9]/g, '');">
                                        @error('dui')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                    {{-- CV --}}
                                    <div class="mb-3">
                                        <label for="cv" class="form-label">Curriculum vitae: <span class="text-danger">*</span></label>
                                        <input type="file" class="form-control @error('cv') is-invalid @enderror" id="cv" name="cv" accept="application/pdf">
                                        @error('cv')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                        @enderror

                                        {{-- Mostrar el CV actual si existe --}}
                                        @if (Auth::user()->participantProfile->cv_file)
                                            <div class="mt-2">
                                                <p class="mb-0">
                                                    <i class="fas fa-file-pdf text-danger"></i> 
                                                    CV actual: 
                                                    {{-- <a href="{{ route('download-cv', ['user_id' => Auth::user()->id, 'cv_file' => Auth::user()->participantProfile->cv_file]) }}" target="_blank" rel="noopener noreferrer">
                                                        {{ basename(Auth::user()->participantProfile->cv_file) }}
                                                    </a> --}}
                                                    <a href="{{ route('profile.download.cv') }}" class="btn btn-primary">
                                                        <i class="fas fa-file-download"></i> Descargar CV
                                                    </a>
                                                </p>
                                            </div>
                                        @endif
                                        
                                        <ul>
                                            <li>El único formato permitido es PDF.</li>
                                            <li>Asegúrate de que tu información esté actualizada.</li>
                                        </ul>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary" type="submit"><i class="fas fa-envelope"></i> ACTUALIZAR MI INFORMACIÓN</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <br>
            <div class=" wow fadeInUp" data-wow-delay="0.1s">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Actualizar correo electrónico</h5>
                            </div>
                            <div class="card-body">
                                <p>Para actualizar tu correo electrónico, debes ingresar tu nuevo correo electrónico y enviar un código de verificación.</p>
                                <form method="POST" action="{{ route('profile.request.update.email') }}">
                                    @csrf
                                    <div class="row">
                                        <div class="col-md-12">
                                            <p>Correo electrónico actual: <b>{{ Auth::user()->email }}</b></p>
                                            <div class="mb-3">
                                                <label for="email" class="form-label">Nuevo correo electrónico: <span class="text-danger">*</span></label>
                                                <input
                                                    type="email"
                                                    class="form-control @error('email') is-invalid @enderror"
                                                    id="email"
                                                    name="email"
                                                    value="{{ old('email') }}"
                                                    aria-describedby="Correo electrónico"
                                                >
                                                @error('email')
                                                    <div class="invalid-feedback">
                                                        {{ $message }}
                                                    </div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary" type="submit"><i class="fas fa-envelope"></i> ENVIAR CÓDIGO DE VERIFICACIÓN</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <br>
            <div class=" wow fadeInUp" data-wow-delay="0.1s">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Actualizar contraseña</h5>
                            </div>
                            <div class="card-body">
                                <p>Para actualizar tu contraseña, debes ingresar tu contraseña actual, la nueva contraseña y la confirmación de la nueva contraseña.</p>
                                <form method="POST" action="{{ route('profile.update.password') }}">
                                    @csrf
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="current_password" class="form-label">Contraseña actual: <span class="text-danger">*</span></label>
                                                <input 
                                                    type="password" 
                                                    class="form-control @error('password') is-invalid @enderror" 
                                                    id="current_password" 
                                                    name="current_password" 
                                                    required>
                                                @error('current_password')
                                                    <div class="invalid-feedback d-block">
                                                        {{ $message }}
                                                    </div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="password" class="form-label">Contraseña: <span class="text-danger">*</span></label>
                                                <input 
                                                    type="password" 
                                                    class="form-control @error('password') is-invalid @enderror" 
                                                    id="password" 
                                                    name="password" 
                                                    required>
                                                <div id="password-feedback" class="form-text">
                                                    <span id="length-feedback" style="display: none; color: red;">La contraseña debe tener al menos 8 caracteres.</span><p>
                                                    <span id="uppercase-feedback" style="display: none; color: red;">La contraseña debe tener al menos una mayúscula.</span><p>
                                                    <span id="number-feedback" style="display: none; color: red;">La contraseña debe tener al menos un número.</span><p>
                                                </div>
                                                @error('password')
                                                    <div class="invalid-feedback d-block">
                                                        {{ $message }}
                                                    </div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="password_confirmation" class="form-label">Confirmar contraseña: <span class="text-danger">*</span></label>
                                                <input 
                                                    type="password" 
                                                    class="form-control @error('password') is-invalid @enderror" 
                                                    id="password_confirmation" 
                                                    name="password_confirmation" 
                                                    required>
                                                <div id="password-match-feedback" class="form-text">
                                                    <span id="match-feedback" style="display: none; color: red;">Las contraseñas no coinciden.</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="showPassword">
                                                <label class="form-check-label" for="showPassword">Mostrar contraseña</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary" type="submit"><i class="fas fa-envelope"></i> ACTUALIZAR CONTRASEÑA</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif

@endsection

{{-- END CONTENT FOR DETAIL BLOG --}}

@section('js')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const currentPasswordInput = document.getElementById('current_password');
    const passwordInput = document.getElementById('password');
    const passwordConfirmationInput = document.getElementById('password_confirmation');
    const showPasswordCheckbox = document.getElementById('showPassword');
    const registerButton = document.getElementById('registerButton');

    // Referencias a los mensajes de feedback
    const lengthFeedback = document.getElementById('length-feedback');
    const uppercaseFeedback = document.getElementById('uppercase-feedback');
    const numberFeedback = document.getElementById('number-feedback');
    const matchFeedback = document.getElementById('match-feedback');

    // Variables de estado
    let isLengthValid = false;
    let isUppercaseValid = false;
    let isNumberValid = false;
    let isMatchValid = false;

    // Función principal de validación de contraseña
    function validatePassword() {
        const password = passwordInput.value;
        const confirmation = passwordConfirmationInput.value;

        // Validar longitud
        isLengthValid = password.length >= 8;
        lengthFeedback.style.display = isLengthValid ? 'none' : 'inline';

        // Validar mayúscula
        isUppercaseValid = /[A-Z]/.test(password);
        uppercaseFeedback.style.display = isUppercaseValid ? 'none' : 'inline';

        // Validar número
        isNumberValid = /[0-9]/.test(password);
        numberFeedback.style.display = isNumberValid ? 'none' : 'inline';

        // Validar coincidencia
        isMatchValid = password === confirmation && confirmation.length > 0;
        matchFeedback.style.display = isMatchValid ? 'none' : 'inline';
        
        // Si las contraseñas coinciden, quitar error de coincidencia
        if (isMatchValid) {
            passwordConfirmationInput.classList.remove('is-invalid');
        } else {
            passwordConfirmationInput.classList.add('is-invalid');
        }

        // Si la contraseña no es válida, error
        if (isLengthValid) {
            passwordInput.classList.remove('is-invalid');
        } else {
            passwordInput.classList.add('is-invalid');
        }

        updateButtonState();
    }

    // Función para habilitar o deshabilitar el botón de registro
    function updateButtonState() {
        if (isLengthValid && isUppercaseValid && isNumberValid && isMatchValid) {
            registerButton.disabled = false;
            registerButton.classList.remove('disabled');
        } else {
            registerButton.disabled = true;
            registerButton.classList.add('disabled');
        }
    }

    // Escuchadores de eventos
    passwordInput.addEventListener('input', validatePassword);
    passwordConfirmationInput.addEventListener('input', validatePassword);

    showPasswordCheckbox.addEventListener('change', function() {
        const type = this.checked ? 'text' : 'password';
        currentPasswordInput.type = type;
        passwordInput.type = type;
        passwordConfirmationInput.type = type;
    });
});
</script>
@endsection

{{-- END BODY --}}