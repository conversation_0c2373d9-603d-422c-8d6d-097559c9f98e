@extends('web.master.master')

@section('title', 'Recuperar contraseña')

@section('cs')
    {{-- additional css --}}
@endsection

{{-- BODY --}}
{{-- CONTENT FOR DETAIL BLOG--}}
@section('content')
<div class="container-fluid blog pb-5">
        <br><br>
        <div class="container pb-5">
            <div class="text-center mx-auto pb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 800px;">
                <h1 class="display-4 mb-4">Recuperar contraseña</h1>
                <p>
                    Ingresa el código de verificación que te hemos enviado a tu correo electrónico y crea una nueva contraseña para tu cuenta.
                </p>
            </div>
            <div class="mx-auto pb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 800px;">
                <h4 class="text-primary">Formulario de recuperación de contraseña</h4>
                <form method="POST" action="{{ route('verify.token.reset.password') }}">
                    @csrf

                    <div class="mb-3">
                        <label for="token" class="form-label">Código de verificación: <span class="text-danger">*</span></label>
                        <input 
                            type="text" 
                            class="form-control @error('token') is-invalid @enderror" 
                            id="token" 
                            name="token" 
                            pattern="[0-9]{8}" 
                            inputmode="numeric" 
                            maxlength="8" 
                            oninput="this.value = this.value.replace(/[^0-9]/g, '');" 
                            value="{{ old('token') }}" 
                            required>
                        @error('token')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="password" class="form-label">Contraseña: <span class="text-danger">*</span></label>
                                <input 
                                    type="password" 
                                    class="form-control @error('password') is-invalid @enderror" 
                                    id="password" 
                                    name="password" 
                                    required>
                                <div id="password-feedback" class="form-text">
                                    <span id="length-feedback" style="display: none; color: red;">La contraseña debe tener al menos 8 caracteres.</span><p>
                                    <span id="uppercase-feedback" style="display: none; color: red;">La contraseña debe tener al menos una mayúscula.</span><p>
                                    <span id="number-feedback" style="display: none; color: red;">La contraseña debe tener al menos un número.</span><p>
                                </div>
                                @error('password')
                                    <div class="invalid-feedback d-block">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="password_confirmation" class="form-label">Confirmar contraseña: <span class="text-danger">*</span></label>
                                <input 
                                    type="password" 
                                    class="form-control @error('password') is-invalid @enderror" 
                                    id="password_confirmation" 
                                    name="password_confirmation" 
                                    required>
                                <div id="password-match-feedback" class="form-text">
                                    <span id="match-feedback" style="display: none; color: red;">Las contraseñas no coinciden.</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="showPassword">
                                <label class="form-check-label" for="showPassword">Mostrar contraseña</label>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" type="submit" id="registerButton" disabled>
                            <i class="fas fa-check"></i> ACTUALIZAR CONTRASEÑA
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

{{-- END CONTENT FOR DETAIL BLOG --}}

@section('js')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('password');
    const passwordConfirmationInput = document.getElementById('password_confirmation');
    const showPasswordCheckbox = document.getElementById('showPassword');
    const registerButton = document.getElementById('registerButton');

    // Referencias a los mensajes de feedback
    const lengthFeedback = document.getElementById('length-feedback');
    const uppercaseFeedback = document.getElementById('uppercase-feedback');
    const numberFeedback = document.getElementById('number-feedback');
    const matchFeedback = document.getElementById('match-feedback');

    // Variables de estado
    let isLengthValid = false;
    let isUppercaseValid = false;
    let isNumberValid = false;
    let isMatchValid = false;

    // Función principal de validación de contraseña
    function validatePassword() {
        const password = passwordInput.value;
        const confirmation = passwordConfirmationInput.value;

        // Validar longitud
        isLengthValid = password.length >= 8;
        lengthFeedback.style.display = isLengthValid ? 'none' : 'inline';

        // Validar mayúscula
        isUppercaseValid = /[A-Z]/.test(password);
        uppercaseFeedback.style.display = isUppercaseValid ? 'none' : 'inline';

        // Validar número
        isNumberValid = /[0-9]/.test(password);
        numberFeedback.style.display = isNumberValid ? 'none' : 'inline';

        // Validar coincidencia
        isMatchValid = password === confirmation && confirmation.length > 0;
        matchFeedback.style.display = isMatchValid ? 'none' : 'inline';
        
        // Si las contraseñas coinciden, quitar error de coincidencia
        if (isMatchValid) {
            passwordConfirmationInput.classList.remove('is-invalid');
        } else {
            passwordConfirmationInput.classList.add('is-invalid');
        }

        // Si la contraseña no es válida, error
        if (isLengthValid) {
            passwordInput.classList.remove('is-invalid');
        } else {
            passwordInput.classList.add('is-invalid');
        }

        updateButtonState();
    }

    // Función para habilitar o deshabilitar el botón de registro
    function updateButtonState() {
        if (isLengthValid && isUppercaseValid && isNumberValid && isMatchValid) {
            registerButton.disabled = false;
            registerButton.classList.remove('disabled');
        } else {
            registerButton.disabled = true;
            registerButton.classList.add('disabled');
        }
    }

    // Escuchadores de eventos
    passwordInput.addEventListener('input', validatePassword);
    passwordConfirmationInput.addEventListener('input', validatePassword);

    showPasswordCheckbox.addEventListener('change', function() {
        const type = this.checked ? 'text' : 'password';
        passwordInput.type = type;
        passwordConfirmationInput.type = type;
    });
});
</script>
@endsection

{{-- END BODY --}}