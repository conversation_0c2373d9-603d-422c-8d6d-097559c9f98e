@extends('web.master.master')

@section('title', 'Iniciar sesion')

@section('cs')
    {{-- additional css --}}
@endsection

{{-- BODY --}}
@section('content')
    {{-- si esta logueado redirigir a perfil --}}
    @if (Auth::check())
        <script>
            window.location.href = "{{ route('profile-general-user') }}";
        </script>
    @endif

    <div class="container-fluid bg-breadcrumb" style="background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url({{ asset('img/header/header1.jpg') }});">
        <div class="bg-breadcrumb-single"></div>
        <div class="container text-center py-5" style="max-width: 900px;">
        <h4 class="text-white display-4 mb-4 wow fadeInDown" data-wow-delay="0.1s">
                Recuperar contraseña - Avance y Desempeño
            </h4>
            <ol class="breadcrumb justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
                {{-- <li class="breadcrumb-item" style="color: #fff">Inicio de sesión</li> --}}
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Inicio</a></li>
                <li class="breadcrumb-item active text-white">Recuperar contraseña</li>
            </ol>    
        </div>
    </div>
    <div class="container-fluid blog pb-5">
        <br><br>
        <div class="container pb-5">
            <div class="text-center mx-auto pb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 800px;">
                <h4 class="text-primary">Recuperar contraseña</h4>
                <p style="color: black">
                    Ingresa tu correo electrónico para enviarte un enlace para cambiar tu contraseña.
                </p>
            </div>
            <form method="POST" action="{{ route('send.email.new.password.temporal') }}">
                @csrf
                <div class="mb-3">
                    <label for="email" class="form-label">Correo electrónico</label>
                    <input 
                        type="email" 
                        class="form-control @error('email') is-invalid @enderror" 
                        id="email" 
                        name="email" 
                        value="{{ old('email') }}" 
                        required>
                    @error('email')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                    @enderror
                    {{-- iniciar sesion --}}
                    <p>¿Recuerdas tu contraseña? <a href="{{ route('acceder') }}" class="text-primary">Inicia sesión</a></p>
                </div>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" type="submit" id="sendEmailNewPasswordTemporal" disabled>
                        <i class="fas fa-check"></i> ENVIAR CAMBIO DE CONTRASEÑA
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('js')
{{-- si esta vacio email no habilitar boton submit --}}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const emailInput = document.getElementById('email');
        const sendEmailNewPasswordTemporalButton = document.getElementById('sendEmailNewPasswordTemporal');

        emailInput.addEventListener('input', function() {
            if (this.value.trim() !== '') {
                sendEmailNewPasswordTemporalButton.disabled = false;
                sendEmailNewPasswordTemporalButton.classList.remove('disabled');
            } else {
                sendEmailNewPasswordTemporalButton.disabled = true;
                sendEmailNewPasswordTemporalButton.classList.add('disabled');
            }
        });
    });
</script>
@endsection

{{-- END BODY --}}