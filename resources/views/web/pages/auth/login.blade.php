@extends('web.master.master')

@section('title', 'Iniciar sesion')

@section('cs')
    {{-- additional css --}}
@endsection

{{-- BODY --}}
@section('content')
    <div class="container-fluid blog pb-5">
        <br><br>
        <div class="container pb-5">
            <div class="text-center mx-auto pb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 800px;">
                <h4 class="text-primary">Iniciar sesión</h4>
                <p>
                    Inicia sesión en tu cuenta para acceder a nuestros servicios.
                </p>
            </div>
            <form method="POST" action="{{ route('login-general-user') }}">
                @csrf
                
                <div class="mb-3">
                    <label for="email" class="form-label">Correo electrónico</label>
                    <input 
                        type="email" 
                        class="form-control @error('email') is-invalid @enderror" 
                        id="email" 
                        name="email" 
                        value="{{ old('email') }}" 
                        required>
                    @error('email')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                    @enderror
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">Contraseña</label>
                    <input 
                        type="password" 
                        class="form-control @error('password') is-invalid @enderror" 
                        id="password" 
                        name="password" 
                        required>
                    @error('password')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="showPassword">
                    <label class="form-check-label" for="showPassword">Mostrar contraseña</label>
                </div>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-check"></i> INICIAR SESIÓN
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('js')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
        const showPasswordCheckbox = document.getElementById('showPassword');
        const passwordInput = document.getElementById('password');

        showPasswordCheckbox.addEventListener('change', function() {
            if (this.checked) {
                passwordInput.type = 'text';
            } else {
                passwordInput.type = 'password';
            }
        });
    });
    </script>
@endsection

{{-- END BODY --}}