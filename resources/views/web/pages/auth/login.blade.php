@extends('web.master.master')

@section('title', 'Iniciar sesion')

@section('cs')
    {{-- additional css --}}
@endsection

{{-- BODY --}}
@section('content')
    {{-- si esta logueado redirigir a perfil --}}
    @if (Auth::check())
        <script>
            window.location.href = "{{ route('profile-general-user') }}";
        </script>
    @endif

    <div class="container-fluid bg-breadcrumb" style="background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url({{ asset('img/header/header1.jpg') }});">
        <div class="bg-breadcrumb-single"></div>
        <div class="container text-center py-5" style="max-width: 900px;">
        <h4 class="text-white display-4 mb-4 wow fadeInDown" data-wow-delay="0.1s">
                Acceder - Avance y Desempeño
            </h4>
            <ol class="breadcrumb justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
                {{-- <li class="breadcrumb-item" style="color: #fff">Inicio de sesión</li> --}}
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Inicio</a></li>
                <li class="breadcrumb-item active text-white">Inicio de sesión</li>
            </ol>    
        </div>
    </div>
    <div class="container-fluid blog pb-5">
        <br><br>
        <div class="container pb-5">
            <div class="text-center mx-auto pb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 800px;">
                <h4 class="text-primary">Iniciar sesión</h4>
                <p style="color: black">
                    Inicia sesión en tu cuenta para acceder a nuestros servicios.
                </p>
            </div>
            <form method="POST" action="{{ route('login-general-user') }}">
                @csrf
                <div class="mb-3">
                    <label for="email" class="form-label">Correo electrónico</label>
                    <input 
                        type="email" 
                        class="form-control @error('email') is-invalid @enderror" 
                        id="email" 
                        name="email" 
                        value="{{ old('email') }}" 
                        required>
                    @error('email')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                    @enderror
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">Contraseña</label>
                    <input 
                        type="password" 
                        class="form-control @error('password') is-invalid @enderror" 
                        id="password" 
                        name="password" 
                        required>
                    @error('password')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                    @enderror
                    <a href="{{ route('forgot-password-general-user') }}" class="text-primary">¿Olvidaste tu contraseña?</a>
                </div>

                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="showPassword">
                    <label class="form-check-label" for="showPassword">Mostrar contraseña</label>
                </div>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-check"></i> INICIAR SESIÓN
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('js')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
        const showPasswordCheckbox = document.getElementById('showPassword');
        const passwordInput = document.getElementById('password');

        showPasswordCheckbox.addEventListener('change', function() {
            if (this.checked) {
                passwordInput.type = 'text';
            } else {
                passwordInput.type = 'password';
            }
        });
    });
    </script>
@endsection

{{-- END BODY --}}