<!-- Services Start -->
<div class="container-fluid service py-5">
    <div class="container py-5">
        <div class="text-center mx-auto pb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 800px;">
            <h4 class="text-primary">{{ $servicios->sub_title }}</h4>
            <h1 class="display-4">{{ $servicios->title }}</h1>
            <p style="color: #000">
                {{ $servicios->content }}
            </p>
        </div>
        <div class="row g-4 justify-content-center text-center">
            @foreach ($detalleServicios as $detalles)
                <div class="col-md-6 col-lg-4 col-xl-4 wow fadeInUp" data-wow-delay="0.1s">
                    <div class="service-item bg-light rounded">
                        <div class="service-img">
                            <img src="{{ asset('img/'.$detalles->cover_image) }}" class="img-fluid w-100 rounded-top" alt="">
                        </div>
                        <div class="service-content text-center p-4">
                            <div class="service-content-inner">
                                <a href="#" class="h4 mb-4 d-inline-flex text-start">
                                    {{ $detalles->title }}
                                </a>
                                <p class="mb-4">
                                    {{ $detalles->content }}
                                </p>
                                @if ($detalles->button == true)
                                    <a class="btn btn-light rounded-pill py-2 px-4" href="{{ route($detalles->button_url) }}">{{ $detalles->button_text }}</a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>
<!-- Services End -->