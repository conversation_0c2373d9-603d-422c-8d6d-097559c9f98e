<!-- Blog Start -->
<div class="container-fluid blog pb-5">
    <br><br>
    <div class="container pb-5">
        <div class="text-center mx-auto pb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 800px;">
            <h4 class="text-primary">Nuestro blog</h4>
            <h1 class="display-4">Últimos artículos y noticias de nuestro blog</h1>
        </div>
        <div class="row g-4 justify-content-center">
            @foreach ($blogPosts as $post)
                <div class="col-md-6 col-lg-6 col-xl-4 wow fadeInUp" data-wow-delay="0.1s">
                    <div class="blog-item bg-light rounded p-4" style="background-image: url(img/bg.png);">
                        <div class="mb-4">
                            <div class="d-flex justify-content-between">
                                <p class="mb-0"><span class="text-dark fw-bold">{{ $post->category->name }}</span></p>
                                <p class="mb-0">Publicado el: <span class="text-dark fw-bold">{{ $post->published_at}}</span></p>
                            </div>
                        </div>
                        <div class="project-img">
                            <picture>
                                <img src="{{ Storage::url($post->image_path) }}" class="img-fluid w-100 rounded" media="(min-width: 768px)" alt="{{ $post->title }}">
                            </picture>
                        </div>
                        <div class="my-4">
                            <a href="{{ route('blog.detail', $post->slug) }}" class="h4">{{ $post->title }}</a>
                        </div>
                        <a class="btn btn-primary rounded-pill py-2 px-4" href="{{ route('blog.detail', $post->slug) }}">Leer más</a>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>
<!-- Blog End -->