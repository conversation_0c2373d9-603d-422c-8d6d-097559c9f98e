<!-- FAQs Start -->
<div class="container-fluid faq py-5">
    <div class="container py-5">
        <div class="row g-5">
            {{-- Columna izquierda --}}
            <div class="col-lg-6 wow fadeInLeft" data-wow-delay="0.2s">
                <div class="h-100">
                    <div class="accordion" id="accordionExampleLeft">
                        @foreach ($primeraMitad as $detalle)
                            <div class="accordion-item mb-2">
                                <h2 class="accordion-header" id="heading{{$detalle->id}}">
                                    <button class="accordion-button collapsed border-0" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{$detalle->id}}" aria-expanded="false" aria-controls="collapse{{$detalle->id}}" style="background-color: #A6C6E2; color: #17385A;">
                                        {{ $detalle->title }}
                                    </button>
                                </h2>
                                <div id="collapse{{$detalle->id}}" class="accordion-collapse collapse" aria-labelledby="heading{{$detalle->id}}" data-bs-parent="#accordionExampleLeft">
                                    <div class="accordion-body rounded">
                                        {!! $detalle->content !!}
                                        @php
                                            // Obtener todas las imágenes relacionadas con esta subsección
                                            $images = $imagesConsultorias->where('subsection_id', $detalle->id);
                                        @endphp
                                        @if($images->count() > 0)
                                        <br>
                                        @foreach($images as $image)
                                        <div class="gallery-item w-100 mb-3">
                                            <div class="gallery-img">
                                                <img src="{{ asset($image->image) }}" alt="{{ $detalle->title }}" style="width: 300px">
                                                <div class="gallery-overlay">
                                                    <a href="{{ asset($image->image) }}" data-lightbox="gallery-{{$detalle->id}}" class="gallery-btn">
                                                        <i class="fas fa-search-plus text-white"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            {{-- Columna derecha --}}
            <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.4s">
                <div class="h-100">
                    <div class="accordion" id="accordionExampleRight">
                        @foreach ($segundaMitad as $detalle)
                            <div class="accordion-item mb-2">
                                <h2 class="accordion-header" id="heading{{$detalle->id}}">
                                    <button class="accordion-button collapsed border-0" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{$detalle->id}}" aria-expanded="false" aria-controls="collapse{{$detalle->id}}" style="background-color: #A6C6E2; color: #17385A;">
                                        {{ $detalle->title }}
                                    </button>
                                </h2>
                                <div id="collapse{{$detalle->id}}" class="accordion-collapse collapse" aria-labelledby="heading{{$detalle->id}}" data-bs-parent="#accordionExampleRight">
                                    <div class="accordion-body rounded">
                                        {!! $detalle->content !!}
                                        @php
                                            $images = $imagesConsultorias->where('subsection_id', $detalle->id);
                                        @endphp
                                        @if($images->count() > 0)
                                        <br>
                                        @foreach($images as $image)
                                        <div class="gallery-item w-100 mb-3">
                                            <div class="gallery-img">
                                                <img src="{{ asset($image->image) }}" alt="{{ $detalle->title }}" style="width: 300px">
                                                <div class="gallery-overlay">
                                                    <a href="{{ asset($image->image) }}" data-lightbox="gallery-{{$detalle->id}}" class="gallery-btn">
                                                        <i class="fas fa-search-plus text-white"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        {{-- Fila para la consultoría impar, solo si existe --}}
        @if ($consultoriaImpar)
        <br>
        <div class="row g-5">
            <div class="col-lg-12 wow fadeInRight" data-wow-delay="0.4s">
                <div class="h-100">
                    <div class="accordion" id="accordionExampleCenter">
                        <div class="accordion-item mb-2">
                            <h2 class="accordion-header" id="heading{{$consultoriaImpar->id}}">
                                <button class="accordion-button collapsed border-0" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{$consultoriaImpar->id}}" aria-expanded="false" aria-controls="collapse{{$consultoriaImpar->id}}" style="background-color: #A6C6E2; color: #17385A;">
                                    {{ $consultoriaImpar->title }}
                                </button>
                            </h2>
                            <div id="collapse{{$consultoriaImpar->id}}" class="accordion-collapse collapse" aria-labelledby="heading{{$consultoriaImpar->id}}" data-bs-parent="#accordionExampleCenter">
                                <div class="accordion-body rounded">
                                    {!! $consultoriaImpar->content !!}
                                    @php
                                        $images = $imagesConsultorias->where('subsection_id', $consultoriaImpar->id);
                                    @endphp
                                    @if($images->count() > 0)
                                    <br>
                                    @foreach($images as $image)
                                    <div class="gallery-item w-100 mb-3">
                                        <div class="gallery-img">
                                            <img src="{{ asset($image->image) }}" alt="{{ $consultoriaImpar->title }}" style="width: 300px">
                                            <div class="gallery-overlay">
                                                <a href="{{ asset($image->image) }}" data-lightbox="gallery-{{$consultoriaImpar->id}}" class="gallery-btn">
                                                    <i class="fas fa-search-plus text-white"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Lightbox Modal -->
<div id="lightboxModal" class="lightbox-modal" onclick="closeLightbox()">
    <div class="lightbox-content">
        <span class="lightbox-close" onclick="closeLightbox()">&times;</span>
        <img id="lightboxImage" src="" alt="">
    </div>
</div>
<!-- FAQs End -->

<style>
/* Custom Gallery Carousel Styles */
.custom-gallery-carousel {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

.gallery-container {
    position: relative;
    width: 100%;
}

.gallery-slides {
    position: relative;
    width: 100%;
    height: auto;
}

.gallery-slide {
    display: none;
    width: 100%;
    padding: 0 15px;
}

.gallery-slide.active {
    display: block;
}

/* Gallery Items */
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    transition: all 0.3s ease;
    /* box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08); */
    height: 100%;
    display: flex;
    flex-direction: column;
}

.gallery-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(23, 56, 90, 0.15);
}

.gallery-img {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    height: 300px;
    background: #FFF;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.gallery-img img {
    width: 180px !important;
    height: auto;
    object-fit: cover;
    object-position: center;
    border-radius: 10px;
    transition: transform 0.4s ease;
}

.gallery-item:hover .gallery-img img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(23, 56, 90, 0.9), rgba(166, 198, 226, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
    border-radius: 10px;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: #17385A;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.gallery-btn:hover {
    background: #A6C6E2;
    transform: scale(1.2);
    box-shadow: 0 8px 25px rgba(11, 225, 255, 0.3);
}

.gallery-btn i {
    font-size: 20px;
    color: white;
}

/* Indicadores de slide */
.gallery-indicators {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-top: 40px;
    padding: 10px 0; /* Espacio adicional para el scale */
    min-height: 40px; /* Altura mínima para acomodar el scale */
}

.indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #FFF;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    flex-shrink: 0; /* Evitar que se compriman */
}

.indicator.active {
    background: #17385A;
    transform: scale(1.3);
    box-shadow: 0 4px 15px rgba(23, 56, 90, 0.3);
}

.indicator:hover {
    background: #17385A;
    transform: scale(1.1);
}

/* Lightbox Modal */
.lightbox-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    animation: fadeIn 0.3s ease;
}

.lightbox-content {
    position: relative;
    margin: auto;
    padding: 20px;
    width: 90%;
    max-width: 1200px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.lightbox-close {
    position: absolute;
    top: 20px;
    right: 35px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10000;
}

.lightbox-close:hover {
    color: #0BFFFF;
}

/* Section styling */
.gallery {
    background: linear-gradient(135deg, #f8f9fa 0%, rgba(166, 198, 226, 0.1) 100%);
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .gallery-img {
        height: 150px;
    }
}

@media (max-width: 768px) {
    .gallery-img {
        height: 130px;
    }

    .gallery-btn {
        width: 50px;
        height: 50px;
    }

    .gallery-btn i {
        font-size: 16px;
    }

    .gallery-indicators {
        margin-top: 30px;
        gap: 12px;
        padding: 8px 0;
        min-height: 35px;
    }

    .indicator {
        width: 14px;
        height: 14px;
    }
}

@media (max-width: 576px) {
    .gallery-img {
        height: 110px;
    }

    .gallery-btn {
        width: 45px;
        height: 45px;
    }

    .gallery-btn i {
        font-size: 14px;
    }

    .gallery-indicators {
        margin-top: 25px;
        gap: 10px;
        padding: 6px 0;
        min-height: 30px;
    }

    .indicator {
        width: 12px;
        height: 12px;
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
</style>

<script>
// Custom Gallery Carousel JavaScript
let currentSlide = 0;
let slides = [];
let indicators = [];
let totalSlides = 0;

// Función para inicializar elementos
function initializeElements() {
    slides = document.querySelectorAll('.gallery-slide');
    indicators = document.querySelectorAll('.indicator');
    totalSlides = slides.length;

    // Limpiar todos los estados activos
    slides.forEach(slide => slide.classList.remove('active'));
    indicators.forEach(indicator => indicator.classList.remove('active'));

    // Establecer el primer slide como activo
    currentSlide = 0;
    if (slides.length > 0 && indicators.length > 0) {
        slides[0].classList.add('active');
        indicators[0].classList.add('active');
    }
}

// Función para ir a un slide específico
function goToSlide(slideIndex) {
    // Validar índice
    if (slideIndex < 0 || slideIndex >= totalSlides) {

        return;
    }

    // Remover clase active de todos los slides e indicadores
    slides.forEach(slide => slide.classList.remove('active'));
    indicators.forEach(indicator => indicator.classList.remove('active'));

    // Actualizar índice actual
    currentSlide = slideIndex;

    // Activar el slide e indicador correspondiente
    if (slides[currentSlide]) {
        slides[currentSlide].classList.add('active');
    }
    if (indicators[currentSlide]) {
        indicators[currentSlide].classList.add('active');
    }
}
// Función para abrir lightbox
function openLightbox(imageSrc) {
    const modal = document.getElementById('lightboxModal');
    const modalImg = document.getElementById('lightboxImage');
    if (modal && modalImg) {
        modal.style.display = 'block';
        modalImg.src = imageSrc;
    }
}

// Función para cerrar lightbox
function closeLightbox() {
    const modal = document.getElementById('lightboxModal');
    if (modal) {
        modal.style.display = 'none';
    }
}



// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar elementos
    initializeElements();

    // Verificar que tenemos slides
    if (totalSlides === 0) {
        return;
    }

    // Cerrar lightbox con tecla Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeLightbox();
        }
    });
});
</script>
<!-- Gallery End -->
<!-- Gallery End -->
