<div class="container-fluid faq-section py-5 ayd-light-background">
    @foreach ($profesionales as $profesional)
        <div class="container py-5">
            <div class="row g-5 align-items-center">
                {{-- Si la iteración es impar (1, 3, 5...), el texto va a la izquierda --}}
                @if ($loop->odd)
                    <div class="col-xl-6 wow fadeInLeft" data-wow-delay="0.2s">
                        <div class="h-100">
                            <div class="mb-5">
                                <h1 class="display-4 mb-0">{{ $profesional->name }}</h1>
                            </div>
                            <div class="accordion" id="accordionExample">
                                <div class="accordion-item">
                                    <div id="collapseOne" class="accordion-collapse collapse show active" aria-labelledby="headingOne" data-bs-parent="#accordionExample">
                                        <div class="accordion-body rounded">
                                            {!! $profesional->content !!}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-6 wow fadeInRight" data-wow-delay="0.4s">
                        <img src="{{ asset($profesional->image) }}" class="img-fluid w-100" alt="">
                    </div>
                {{-- Si la iteración es par (2, 4, 6...), la imagen va a la izquierda --}}
                @else
                    <div class="col-xl-6 wow fadeInLeft" data-wow-delay="0.4s">
                        <img src="{{ asset($profesional->image) }}" class="img-fluid w-100" alt="">
                    </div>
                    <div class="col-xl-6 wow fadeInRight" data-wow-delay="0.2s">
                        <div class="h-100">
                            <div class="mb-5">
                                <h1 class="display-4 mb-0">{{ $profesional->name }}</h1>
                            </div>
                            <div class="accordion" id="accordionExample">
                                <div class="accordion-item">
                                    <div id="collapseOne" class="accordion-collapse collapse show active" aria-labelledby="headingOne" data-bs-parent="#accordionExample">
                                        <div class="accordion-body rounded">
                                            {!! $profesional->content !!}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @endforeach
</div>