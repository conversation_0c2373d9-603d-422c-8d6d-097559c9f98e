<div class="container-fluid blog pb-5">
    <br><br>
    <div class="container pb-5">
        @if ($employments->isNotEmpty())
        <div class="text-center mx-auto pb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 800px;">
            <h4 class="text-primary">Empleos</h4>
            <h1 class="display-4">Últimas ofertas de empleos disponibles</h1>
        </div>
        @endif
        <div class="row g-4 justify-content-center">
            @forelse ($employments as $employment)
                <div class="col-md-6 col-lg-6 col-xl-4 wow fadeInUp" data-wow-delay="0.1s">
                    <div class="blog-item bg-light rounded p-4" style="background-image: url(img/bg.png);">
                        <div class="mb-4">
                            <div class="d-flex justify-content-between">
                                <p class="mb-0"><span class="text-dark fw-bold">{{ $employment->subtitle }}</span></p>
                                <p class="mb-0">Publicado el: <span class="text-dark fw-bold">{{ $employment->created_at->format('d/m/Y') }}</span></p>
                            </div>
                        </div>
                        <div class="project-img">
                            <picture>
                                <img src="{{ Storage::url($employment->cover_image) }}" class="img-fluid w-100 rounded" media="(min-width: 768px)" alt="{{ $employment->title }}">
                            </picture>
                        </div>
                        <div class="my-4">
                            <a href="{{ route('job.detail', $employment->slug) }}" class="h4">{{ $employment->title }}</a>
                            <p>{{ $employment->short_description }}</p>
                        </div>
                        <a class="btn btn-primary rounded-pill py-2 px-4" href="{{ route('job.detail', $employment->slug) }}">Ver detalles</a>
                    </div>
                </div>
            @empty
                <div class="col-12 text-center">
                    <h1 class="display-4">Sin registros de empleos disponibles</h1>
                </div>
            @endforelse
        </div>
    </div>
</div>