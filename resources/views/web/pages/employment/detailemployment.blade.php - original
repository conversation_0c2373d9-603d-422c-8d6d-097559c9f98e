<div class="container-fluid service py-5">
    <div class="container py-5">
        <div class="text-center mx-auto wow fadeInUp" data-wow-delay="0.2s" style="max-width: 800px;">
            <h4 class="text-primary">{{ $employment->subtitle }}</h4>
            <p class="mb-0"><span class="text-dark fw-bold">Publicado el:</span> {{ $employment->created_at->format('d/m/Y') }}</p>
            <h1 class="display-4 mb-4">{{ $employment->title }}</h1>
        </div>
    </div>
    <div center align="center" class="wow fadeInUp" data-wow-delay="0.4s">
        <img src="{{ Storage::url($employment->cover_image) }}" class="img-fluid w-100 rounded-top bg-white" style="max-width: 600px;" alt="Image">
    </div>
    <div class="container py-5">
        <div class="row">
            <div class="col-md-12 mx-auto wow fadeInUp" data-wow-delay="0.1s">
                <div class="rounded h-100">
                    <div class="black-text">
                        {!! $employment->description !!}
                    </div>
                </div>
            </div>
            <div class="col-md-12 wow fadeInUp" data-wow-delay="0.1s">
                <P></P>
                @if (Auth::check())
                    @if (Auth::user()->hasRole('Participante'))
                        @if (Auth::user()->participantProfile?->phonenumber != null && Auth::user()->participantProfile?->cv_file != null && Auth::user()->participantProfile?->dui != null)
                            <form action="{{ route('apply-employment') }}" method="POST">
                                @csrf
                                <input type="hidden" name="id" value="{{ $employment->id }}">
                                <input type="hidden" name="slug" value="{{ $employment->slug }}">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-success" type="submit">
                                        <i class="fas fa-check"></i> APLICAR A ESTE EMPLEO
                                    </button>
                                </div>
                            </form>
                        @else
                            <div class="d-grid gap-2">
                                <a class="btn btn-warning" type="submit" href="{{ route('profile-general-user') }}">
                                    <i class="fas fa-user"></i> DA CLIC AQUI PARA COMPLETAR TU PERFIL: <br> DEBE REGISTRAR SU NÚMERO DE TELÉFONO, DUI Y SUBIR SU CV
                                </a>
                            </div>
                        @endif
                    @else
                        {{-- Si el usuario no es "Participante", pero está autenticado --}}
                        <div class="d-grid gap-2">
                            
                        </div>
                    @endif
                @else
                    <br>
                    <div class="mx-auto pb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 800px;">
                        <h4 class="text-primary">Iniciar sesión para aplicar a este empleo</h4>
                        <form method="POST" action="{{ route('login-general-user') }}">
                            @csrf
                            <div class="mb-3">
                                <label for="email" class="form-label">Correo electrónico</label>
                                <input 
                                    type="email" 
                                    class="form-control @error('email') is-invalid @enderror" 
                                    id="email" 
                                    name="email" 
                                    value="{{ old('email') }}" 
                                    required>
                                @error('email')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Contraseña</label>
                                <input 
                                    type="password" 
                                    class="form-control @error('password') is-invalid @enderror" 
                                    id="password" 
                                    name="password" 
                                    required>
                                @error('password')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="showPassword">
                                <label class="form-check-label" for="showPassword">Mostrar contraseña</label>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-check"></i> INICIAR SESIÓN
                                </button>
                            </div>
                        </form>
                        <br>
                        <h4>¿No tienes una cuenta? <a style="" href="{{ route('registro') }}">Regístrate</a> para aplicar</h4>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
    const showPasswordCheckbox = document.getElementById('showPassword');
    const passwordInput = document.getElementById('password');

    showPasswordCheckbox.addEventListener('change', function() {
        if (this.checked) {
            passwordInput.type = 'text';
        } else {
            passwordInput.type = 'password';
        }
    });
});
</script>