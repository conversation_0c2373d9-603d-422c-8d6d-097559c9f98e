<div class="container-fluid service py-5">
    <div class="container py-5">
        <div class="text-center mx-auto wow fadeInUp" data-wow-delay="0.2s" style="max-width: 800px;">
            <h4 class="text-primary">{{ $employment->subtitle }}</h4>
            <p class="mb-0"><span class="text-dark fw-bold">Publicado el:</span> {{ $employment->created_at->format('d/m/Y') }}</p>
            <h1 class="display-4 mb-4">{{ $employment->title }}</h1>
        </div>
    </div>
    <div center align="center" class="wow fadeInUp" data-wow-delay="0.4s">
        <img src="{{ Storage::url($employment->cover_image) }}" class="img-fluid w-100 rounded-top bg-white" style="max-width: 600px;" alt="Image">
    </div>
    <div class="container py-5">
        <div class="row">
            <div class="col-md-12 mx-auto wow fadeInUp" data-wow-delay="0.1s">
                <div class="rounded h-100">
                    <div class="black-text">
                        {!! $employment->description !!}
                    </div>
                </div>
            </div>
            
            <hr>
            <div class="alert alert-primary text-center" role="alert"> 
                <b>APLICA A ESTE EMPLEO</b>
            </div>
            <div class="row">
                <div class="col-md-8 wow fadeInLeft" data-wow-delay="0.1s">
                    <form method="POST" action="{{ route('update-profile-general-user') }}" enctype="multipart/form-data">
                        @csrf
                        <div class="mb-3">
                            <label for="full_name" class="form-label">Escribe tu nombre completo: <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('full_name') is-invalid @enderror" id="full_name" name="full_name">
                            @error('full_name')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Ingresa tu correo electrónico: <span class="text-danger">*</span></label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email">
                            @error('email')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                        {{-- CV Upload --}}
                        <div class="mb-3">
                            <label for="cv" class="form-label">Sube tu curriculum vitae: <span class="text-danger">*</span></label>

                            <!-- Drag & Drop Area -->
                            <div id="cv-upload-area" class="cv-upload-container">
                                <div class="cv-upload-content">
                                    <div class="cv-upload-icon">
                                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                            <polyline points="14,2 14,8 20,8"></polyline>
                                            <line x1="16" y1="13" x2="8" y2="13"></line>
                                            <line x1="16" y1="17" x2="8" y2="17"></line>
                                            <polyline points="10,9 9,9 8,9"></polyline>
                                        </svg>
                                    </div>
                                    <div class="cv-upload-text">
                                        <h6 class="mb-1">Arrastra tu CV aquí</h6>
                                        <p class="text-muted mb-2">o <span class="cv-browse-link">busca en tu dispositivo</span></p>
                                        <small class="text-muted">Solo archivos PDF • Máximo 5MB</small>
                                    </div>
                                </div>

                                <!-- Hidden File Input -->
                                <input type="file"
                                       class="cv-file-input @error('cv') is-invalid @enderror"
                                       id="cv"
                                       name="cv"
                                       accept=".pdf,application/pdf"
                                       hidden>
                            </div>

                            @error('cv')
                                <div class="invalid-feedback d-block">
                                    {{ $message }}
                                </div>
                            @enderror

                            <!-- File Preview -->
                            <div id="cv-preview" class="cv-preview-container" style="display: none;">
                                <div class="cv-preview-header">
                                    <div class="cv-file-info">
                                        <div class="cv-file-icon">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                                <polyline points="14,2 14,8 20,8"></polyline>
                                            </svg>
                                        </div>
                                        <div class="cv-file-details">
                                            <div class="cv-file-name"></div>
                                            <div class="cv-file-size"></div>
                                        </div>
                                    </div>
                                    <button type="button" class="cv-remove-btn" title="Eliminar archivo">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <line x1="18" y1="6" x2="6" y2="18"></line>
                                            <line x1="6" y1="6" x2="18" y2="18"></line>
                                        </svg>
                                    </button>
                                </div>

                                <!-- PDF Viewer -->
                                <div class="cv-pdf-viewer">
                                    <iframe id="cv-iframe"
                                            src=""
                                            frameborder="0"
                                            width="100%"
                                            height="400">
                                    </iframe>
                                </div>
                            </div>

                            <!-- Upload Progress -->
                            <div id="cv-progress" class="cv-progress-container" style="display: none;">
                                <div class="cv-progress-bar">
                                    <div class="cv-progress-fill"></div>
                                </div>
                                <small class="cv-progress-text">Cargando archivo...</small>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary btn-lg" type="submit"><i class="fas fa-envelope"></i> ENVIAR MI INFORMACIÓN</button>
                        </div>
                    </form>
                </div>
                <div class="col-md-4 wow fadeInRight" data-wow-delay="0.1s">
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        {{-- <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button> --}}
                        <h4 class="alert-heading">💡 Importante!</h4>
                        Se enviará un correo electrónico de confirmación a la dirección de correo electrónico que proporciones, al que debes confirmar para completar tu aplicación a este empleo.</p>
                        <div class="d-flex align-items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-exclamation-triangle-fill flex-shrink-0 me-2" viewBox="0 0 16 16" role="img" aria-label="Warning:">
                            <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
                            </svg>
                            <div>
                                Si no confirmas tu correo electrónico, tu aplicación no será considerada.
                            </div>
                        </div>
                        <hr>
                        <p>Si tienes alguna pregunta, contáctanos a través de nuestro correo electrónico: <a href="mailto:<EMAIL>"><EMAIL></a> o a nuestro número de WhatsApp: <a href="https://api.whatsapp.com/send?phone=50363105831&text=Hola,%20necesito%20ayuda%20con%20la%20aplicación%20a%20un%20empleo.">6310-5831</a>.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* CV Upload Styles */
.cv-upload-container {
    border: 2px dashed #e1e5e9;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    background: #fafbfc;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.cv-upload-container:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.cv-upload-container.dragover {
    border-color: #007bff;
    background: #e3f2fd;
    transform: scale(1.02);
}

.cv-upload-container.error {
    border-color: #dc3545;
    background: #fff5f5;
}

.cv-upload-content {
    pointer-events: none;
}

.cv-upload-icon {
    color: #6c757d;
    margin-bottom: 1rem;
}

.cv-upload-container:hover .cv-upload-icon {
    color: #007bff;
}

.cv-upload-text h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.cv-browse-link {
    color: #007bff;
    text-decoration: underline;
    cursor: pointer;
}

.cv-browse-link:hover {
    color: #0056b3;
}

/* Preview Styles */
.cv-preview-container {
    margin-top: 1rem;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.cv-preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
}

.cv-file-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.cv-file-icon {
    color: #dc3545;
    display: flex;
    align-items: center;
}

.cv-file-details {
    display: flex;
    flex-direction: column;
}

.cv-file-name {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.cv-file-size {
    font-size: 0.8rem;
    color: #6c757d;
}

.cv-remove-btn {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.cv-remove-btn:hover {
    background: #e9ecef;
    color: #dc3545;
}

.cv-pdf-viewer {
    background: #f8f9fa;
}

.cv-pdf-viewer iframe {
    border: none;
    background: white;
}

/* Progress Styles */
.cv-progress-container {
    margin-top: 1rem;
}

.cv-progress-bar {
    width: 100%;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.cv-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    width: 0%;
    transition: width 0.3s ease;
}

.cv-progress-text {
    display: block;
    margin-top: 0.5rem;
    color: #6c757d;
    text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
    .cv-upload-container {
        padding: 1.5rem 1rem;
    }

    .cv-pdf-viewer iframe {
        height: 300px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password toggle functionality
    const showPasswordCheckbox = document.getElementById('showPassword');
    const passwordInput = document.getElementById('password');

    if (showPasswordCheckbox && passwordInput) {
        showPasswordCheckbox.addEventListener('change', function() {
            if (this.checked) {
                passwordInput.type = 'text';
            } else {
                passwordInput.type = 'password';
            }
        });
    }

    // CV Upload functionality
    console.log('Initializing CV upload functionality...');

    const uploadArea = document.getElementById('cv-upload-area');
    const fileInput = document.getElementById('cv');
    const previewContainer = document.getElementById('cv-preview');
    const progressContainer = document.getElementById('cv-progress');
    const progressFill = document.querySelector('.cv-progress-fill');
    const iframe = document.getElementById('cv-iframe');

    console.log('Elements found:', {
        uploadArea: !!uploadArea,
        fileInput: !!fileInput,
        previewContainer: !!previewContainer,
        progressContainer: !!progressContainer,
        progressFill: !!progressFill,
        iframe: !!iframe
    });

    if (!uploadArea || !fileInput) {
        console.error('Required elements not found');
        return;
    }

    // Click to browse
    uploadArea.addEventListener('click', (e) => {
        e.preventDefault();
        fileInput.click();
    });

    // Drag and drop events
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        e.stopPropagation();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', (e) => {
        e.preventDefault();
        e.stopPropagation();
        // Only remove dragover if we're leaving the upload area itself
        if (!uploadArea.contains(e.relatedTarget)) {
            uploadArea.classList.remove('dragover');
        }
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        e.stopPropagation();
        uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFile(files[0]);
        }
    });

    // Prevent default drag behaviors on document
    document.addEventListener('dragover', (e) => {
        e.preventDefault();
    });

    document.addEventListener('drop', (e) => {
        e.preventDefault();
    });

    // File input change
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFile(e.target.files[0]);
        }
    });

    // Remove file button
    document.addEventListener('click', (e) => {
        if (e.target.closest('.cv-remove-btn')) {
            removeFile();
        }
    });

    function handleFile(file) {
        console.log('Handling file:', file.name, file.type, file.size);

        // Validate file type
        if (file.type !== 'application/pdf' && !file.name.toLowerCase().endsWith('.pdf')) {
            showError('Solo se permiten archivos PDF');
            return;
        }

        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            showError('El archivo no puede ser mayor a 5MB');
            return;
        }

        // Clear any previous errors
        clearError();

        // Show progress
        showProgress();

        // Simulate upload progress
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 30;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
                setTimeout(() => {
                    hideProgress();
                    showPreview(file);
                }, 500);
            }
            progressFill.style.width = progress + '%';
        }, 100);

        // Update file input
        const dt = new DataTransfer();
        dt.items.add(file);
        fileInput.files = dt.files;
    }

    function showPreview(file) {
        // Hide upload area
        uploadArea.style.display = 'none';

        // Show preview
        previewContainer.style.display = 'block';

        // Update file info
        document.querySelector('.cv-file-name').textContent = file.name;
        document.querySelector('.cv-file-size').textContent = formatFileSize(file.size);

        // Create object URL for PDF preview
        const fileURL = URL.createObjectURL(file);
        iframe.src = fileURL;
    }

    function removeFile() {
        // Clear file input
        fileInput.value = '';

        // Hide preview
        previewContainer.style.display = 'none';

        // Show upload area
        uploadArea.style.display = 'block';

        // Clear iframe
        iframe.src = '';

        // Clear any errors
        clearError();
    }

    function showProgress() {
        progressContainer.style.display = 'block';
        progressFill.style.width = '0%';
    }

    function hideProgress() {
        progressContainer.style.display = 'none';
    }

    function showError(message) {
        uploadArea.classList.add('error');

        // Remove existing error messages
        const existingError = document.querySelector('.cv-error-message');
        if (existingError) {
            existingError.remove();
        }

        // Add error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'cv-error-message invalid-feedback d-block';
        errorDiv.textContent = message;
        uploadArea.parentNode.appendChild(errorDiv);
    }

    function clearError() {
        uploadArea.classList.remove('error');
        const errorMessage = document.querySelector('.cv-error-message');
        if (errorMessage) {
            errorMessage.remove();
        }
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});
</script>