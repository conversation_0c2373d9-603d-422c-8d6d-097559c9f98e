<div class="container-fluid service py-5">
    <div class="container py-5">
        <div class="text-center mx-auto wow fadeInUp" data-wow-delay="0.2s" style="max-width: 800px;">
            <h4 class="text-primary">{{ $employment->subtitle }}</h4>
            <p class="mb-0"><span class="text-dark fw-bold">Publicado el:</span> {{ $employment->created_at->format('d/m/Y') }}</p>
            <h1 class="display-4 mb-4">{{ $employment->title }}</h1>
        </div>
    </div>
    <div center align="center" class="wow fadeInUp" data-wow-delay="0.4s">
        <img src="{{ Storage::url($employment->cover_image) }}" class="img-fluid w-100 rounded-top bg-white" style="max-width: 600px;" alt="Image">
    </div>
    <div class="container py-5">
        <div class="row">
            <div class="col-md-12 mx-auto wow fadeInUp" data-wow-delay="0.1s">
                <div class="rounded h-100">
                    <div class="black-text">
                        {!! $employment->description !!}
                    </div>
                </div>
            </div>
            
            <hr>
            <div class="alert alert-primary text-center" role="alert"> 
                <b>APLICA A ESTE EMPLEO</b>
            </div>
            <div class="row">
                <div class="col-md-8 wow fadeInLeft" data-wow-delay="0.1s">
                    <form method="POST" action="{{ route('update-profile-general-user') }}" enctype="multipart/form-data">
                        @csrf
                        <div class="mb-3">
                            <label for="full_name" class="form-label">Escribe tu nombre completo: <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('full_name') is-invalid @enderror" id="full_name" name="full_name">
                            @error('full_name')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Ingresa tu correo electrónico: <span class="text-danger">*</span></label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email">
                            @error('email')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                        {{-- CV --}}
                        <div class="mb-3">
                            <label for="cv" class="form-label">Sube tu curriculum vitae: <span class="text-danger">*</span></label>
                            <input type="file" class="form-control @error('cv') is-invalid @enderror" id="cv" name="cv" accept="application/pdf">
                            @error('cv')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                            <ul>
                                <li>El único formato permitido es PDF.</li>
                                <li>El tamaño máximo del archivo es de 5MB.</li>
                                <li>Asegúrate de que tu información esté actualizada.</li>
                            </ul>
                            <div></div>
                        </div>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary btn-lg" type="submit"><i class="fas fa-envelope"></i> ENVIAR MI INFORMACIÓN</button>
                        </div>
                    </form>
                </div>
                <div class="col-md-4 wow fadeInRight" data-wow-delay="0.1s">
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        {{-- <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button> --}}
                        <h4 class="alert-heading">💡 Importante!</h4>
                        Se enviará un correo electrónico de confirmación a la dirección de correo electrónico que proporciones, al que debes confirmar para completar tu aplicación a este empleo.</p>
                        <div class="d-flex align-items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-exclamation-triangle-fill flex-shrink-0 me-2" viewBox="0 0 16 16" role="img" aria-label="Warning:">
                            <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
                            </svg>
                            <div>
                                Si no confirmas tu correo electrónico, tu aplicación no será considerada.
                            </div>
                        </div>
                        <hr>
                        <p>Si tienes alguna pregunta, contáctanos a través de nuestro correo electrónico: <a href="mailto:<EMAIL>"><EMAIL></a> o a nuestro número de WhatsApp: <a href="https://api.whatsapp.com/send?phone=50363105831&text=Hola,%20necesito%20ayuda%20con%20la%20aplicación%20a%20un%20empleo.">6310-5831</a>.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
    const showPasswordCheckbox = document.getElementById('showPassword');
    const passwordInput = document.getElementById('password');

    showPasswordCheckbox.addEventListener('change', function() {
        if (this.checked) {
            passwordInput.type = 'text';
        } else {
            passwordInput.type = 'password';
        }
    });
});
</script>