<!-- Corporate Start -->
<div class="container-fluid testimonial bg-light py-5">
    <div class="container py-5">
        <div class="row">
            <div class="col-xl-12 wow fadeInLeft" data-wow-delay="0.1s">
                <div class="h-100 rounded">
                    <h4 class="text-primary">Valores</h4>
                    <h1 class="display-4 mb-4">Nuestros valores. Nuestra esencia</h1>
                    <p class="mb-4 black-text">
                        En Avance y Desempeño, nuestra esencia radica en construir relaciones sólidas y ofrecer soluciones de valor que impulsen tu éxito. Nos dedicamos a ser tu aliado estratégico, guiados por principios fundamentales que aseguran la confianza en cada paso, un compromiso inquebrantable con tus metas y la calidad excepcional en todo lo que hacemos.
                    </p>
                </div>
            </div>
        </div>
        <div class="row g-4 align-items-center fadeInRight">
            <div class="col-xl-12">
                <div class="testimonial-carousel owl-carousel wow fadeInUp" data-wow-delay="0.1s">
                    @foreach ($valores as $valor)
                    <div class="testimonial-item bg-white rounded p-4 wow fadeInUp" data-wow-delay="0.3s">
                        <div class="d-flex">
                            <div>
                                <i class="{{ $valor->icon }} fa-3x text-dark me-3"></i>
                            </div>
                            <p class="mt-4 black-text">
                                {{ $valor->description }}
                            </p>
                        </div>
                        <div class="d-flex justify-content-end">
                            <div class="my-auto text-end">
                                <h5>{{ $valor->name }}</h5>
                                <p class="mb-0">Valor</p>
                            </div>
                            <div class="bg-white rounded-circle ms-3">
                                <img src="{{ asset($valor->image) }}" class="rounded-circle p-2" style="width: 80px; height: 80px; border: 1px solid; border-color: var(--bs-primary);" alt="">
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Corporate End -->