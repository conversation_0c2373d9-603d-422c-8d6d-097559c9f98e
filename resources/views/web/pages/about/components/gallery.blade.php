<!-- Gallery Start -->
<div class="container-fluid gallery py-5">
    <div class="container py-5">
        <div class="text-center mx-auto pb-5 wow fadeInUp" data-wow-delay="0.2s" style="max-width: 800px;">
            <h4 class="text-primary">Galería</h4>
            <h1 class="display-4 mb-4">Nuestros Momentos</h1>
            <p class="mb-0">Descubre los momentos que definen nuestra trayectoria. Una colección de imágenes que reflejan nuestro compromiso, profesionalismo y los logros alcanzados junto a nuestros clientes.</p>
        </div>

        <!-- Gallery Carousel -->
        <div class="gallery-carousel-container">
            <div class="owl-carousel gallery-carousel wow fadeInUp" data-wow-delay="0.2s">
                <div class="gallery-item">
                    <div class="gallery-img" onclick="openLightbox('{{ asset('img/gallery/ayd_1.jpg') }}')">
                        <img src="{{ asset('img/gallery/ayd_1.jpg') }}" class="img-fluid" alt="Galería AYD 1">
                        <div class="gallery-overlay">
                            <div class="gallery-btn">
                                <i class="fas fa-search-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-img" onclick="openLightbox('{{ asset('img/gallery/ayd_2.jpg') }}')">
                        <img src="{{ asset('img/gallery/ayd_2.jpg') }}" class="img-fluid" alt="Galería AYD 2">
                        <div class="gallery-overlay">
                            <div class="gallery-btn">
                                <i class="fas fa-search-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-img" onclick="openLightbox('{{ asset('img/gallery/ayd_3.jpg') }}')">
                        <img src="{{ asset('img/gallery/ayd_3.jpg') }}" class="img-fluid" alt="Galería AYD 3">
                        <div class="gallery-overlay">
                            <div class="gallery-btn">
                                <i class="fas fa-search-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-img" onclick="openLightbox('{{ asset('img/gallery/ayd_4.jpg') }}')">
                        <img src="{{ asset('img/gallery/ayd_4.jpg') }}" class="img-fluid" alt="Galería AYD 4">
                        <div class="gallery-overlay">
                            <div class="gallery-btn">
                                <i class="fas fa-search-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-img" onclick="openLightbox('{{ asset('img/gallery/ayd_5.jpg') }}')">
                        <img src="{{ asset('img/gallery/ayd_5.jpg') }}" class="img-fluid" alt="Galería AYD 5">
                        <div class="gallery-overlay">
                            <div class="gallery-btn">
                                <i class="fas fa-search-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-img" onclick="openLightbox('{{ asset('img/gallery/ayd_6.jpg') }}')">
                        <img src="{{ asset('img/gallery/ayd_6.jpg') }}" class="img-fluid" alt="Galería AYD 6">
                        <div class="gallery-overlay">
                            <div class="gallery-btn">
                                <i class="fas fa-search-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-img" onclick="openLightbox('{{ asset('img/gallery/ayd_7.jpg') }}')">
                        <img src="{{ asset('img/gallery/ayd_7.jpg') }}" class="img-fluid" alt="Galería AYD 7">
                        <div class="gallery-overlay">
                            <div class="gallery-btn">
                                <i class="fas fa-search-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-img" onclick="openLightbox('{{ asset('img/gallery/ayd_8.jpg') }}')">
                        <img src="{{ asset('img/gallery/ayd_8.jpg') }}" class="img-fluid" alt="Galería AYD 8">
                        <div class="gallery-overlay">
                            <div class="gallery-btn">
                                <i class="fas fa-search-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-img" onclick="openLightbox('{{ asset('img/gallery/ayd_9.jpg') }}')">
                        <img src="{{ asset('img/gallery/ayd_9.jpg') }}" class="img-fluid" alt="Galería AYD 9">
                        <div class="gallery-overlay">
                            <div class="gallery-btn">
                                <i class="fas fa-search-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-img" onclick="openLightbox('{{ asset('img/gallery/ayd_10.jpg') }}')">
                        <img src="{{ asset('img/gallery/ayd_10.jpg') }}" class="img-fluid" alt="Galería AYD 10">
                        <div class="gallery-overlay">
                            <div class="gallery-btn">
                                <i class="fas fa-search-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-img" onclick="openLightbox('{{ asset('img/gallery/ayd_11.jpg') }}')">
                        <img src="{{ asset('img/gallery/ayd_11.jpg') }}" class="img-fluid" alt="Galería AYD 11">
                        <div class="gallery-overlay">
                            <div class="gallery-btn">
                                <i class="fas fa-search-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-img" onclick="openLightbox('{{ asset('img/gallery/ayd_12.jpg') }}')">
                        <img src="{{ asset('img/gallery/ayd_12.jpg') }}" class="img-fluid" alt="Galería AYD 12">
                        <div class="gallery-overlay">
                            <div class="gallery-btn">
                                <i class="fas fa-search-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-img" onclick="openLightbox('{{ asset('img/gallery/ayd_13.jpg') }}')">
                        <img src="{{ asset('img/gallery/ayd_13.jpg') }}" class="img-fluid" alt="Galería AYD 13">
                        <div class="gallery-overlay">
                            <div class="gallery-btn">
                                <i class="fas fa-search-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="gallery-img" onclick="openLightbox('{{ asset('img/gallery/ayd_14.jpg') }}')">
                        <img src="{{ asset('img/gallery/ayd_14.jpg') }}" class="img-fluid" alt="Galería AYD 14">
                        <div class="gallery-overlay">
                            <div class="gallery-btn">
                                <i class="fas fa-search-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lightbox Modal -->
        <div id="lightboxModal" class="lightbox-modal" onclick="closeLightbox()">
            <div class="lightbox-content">
                <span class="lightbox-close" onclick="closeLightbox()">&times;</span>
                <img id="lightboxImage" src="" alt="">
            </div>
        </div>
    </div>
</div>

<style>
/* Gallery Carousel Styles */
.gallery-carousel-container {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

.gallery-carousel {
    width: 100%;
    margin: 0 auto;
    position: relative;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    height: 350px;
    margin: 0 10px;
    cursor: pointer;
}

.gallery-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(23, 56, 90, 0.2);
}

.gallery-img {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    height: 100%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 15px;
    transition: transform 0.4s ease;
}

.gallery-item:hover .gallery-img img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(23, 56, 90, 0.9), rgba(166, 198, 226, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
    border-radius: 15px;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 70px;
    height: 70px;
    background: #17385A;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.gallery-btn:hover {
    background: #0BFFFF;
    transform: scale(1.2);
    box-shadow: 0 8px 25px rgba(11, 225, 255, 0.3);
}

.gallery-btn i {
    font-size: 24px;
    color: white;
}

/* Lightbox Modal */
.lightbox-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    animation: fadeIn 0.3s ease;
}

.lightbox-content {
    position: relative;
    margin: auto;
    padding: 20px;
    width: 90%;
    max-width: 1200px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.lightbox-close {
    position: absolute;
    top: 20px;
    right: 35px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10000;
}

.lightbox-close:hover {
    color: #0BFFFF;
}

/* Section styling */
.gallery {
    background: linear-gradient(135deg, #f8f9fa 0%, rgba(166, 198, 226, 0.1) 100%);
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .gallery-item {
        height: 280px;
    }
    
    .gallery-btn {
        width: 60px;
        height: 60px;
    }
    
    .gallery-btn i {
        font-size: 20px;
    }
}

@media (max-width: 768px) {
    .gallery-item {
        height: 220px;
        margin: 0 5px;
    }
    
    .gallery-btn {
        width: 50px;
        height: 50px;
    }
    
    .gallery-btn i {
        font-size: 18px;
    }
}

@media (max-width: 576px) {
    .gallery-item {
        height: 180px;
    }
    
    .gallery-btn {
        width: 45px;
        height: 45px;
    }
    
    .gallery-btn i {
        font-size: 16px;
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize gallery carousel
    if (typeof $ !== 'undefined' && $.fn.owlCarousel) {
        const carousel = $('.gallery-carousel').owlCarousel({
            items: 4,
            loop: true,
            margin: 20,
            nav: false,
            dots: false,
            autoplay: true,
            autoplayTimeout: 4000,
            autoplayHoverPause: true,
            mouseDrag: true,
            touchDrag: true,
            smartSpeed: 800,
            responsive: {
                0: {
                    items: 1
                },
                576: {
                    items: 2
                },
                768: {
                    items: 3
                },
                992: {
                    items: 4
                },
                1200: {
                    items: 4
                }
            }
        });

        // Pause autoplay on hover
        $('.gallery-item').on('mouseenter', function() {
            carousel.trigger('stop.owl.autoplay');
        });

        $('.gallery-item').on('mouseleave', function() {
            carousel.trigger('play.owl.autoplay', [4000]);
        });

        // Handle scroll wheel
        let isScrolling = false;
        let scrollTimeout;

        $('.gallery-carousel').on('wheel', function(e) {
            if (isScrolling) return;
            
            isScrolling = true;
            carousel.trigger('stop.owl.autoplay');
            
            if (e.originalEvent.deltaY > 0) {
                carousel.trigger('next.owl.carousel');
            } else {
                carousel.trigger('prev.owl.carousel');
            }
            
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(function() {
                isScrolling = false;
                carousel.trigger('play.owl.autoplay', [4000]);
            }, 300);
        });
    }
});

// Lightbox functions
function openLightbox(imageSrc) {
    const modal = document.getElementById('lightboxModal');
    const modalImg = document.getElementById('lightboxImage');
    modal.style.display = 'block';
    modalImg.src = imageSrc;
    
    // Pause carousel autoplay
    if (typeof $ !== 'undefined' && $('.gallery-carousel').data('owl.carousel')) {
        $('.gallery-carousel').trigger('stop.owl.autoplay');
    }
}

function closeLightbox() {
    const modal = document.getElementById('lightboxModal');
    modal.style.display = 'none';
    
    // Resume carousel autoplay
    if (typeof $ !== 'undefined' && $('.gallery-carousel').data('owl.carousel')) {
        $('.gallery-carousel').trigger('play.owl.autoplay', [4000]);
    }
}

// Close lightbox with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeLightbox();
    }
});
</script>
<!-- Gallery End -->
<!-- Gallery End -->
