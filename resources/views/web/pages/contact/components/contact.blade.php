<div class="container-fluid contact bg-light py-5">
    <div class="container py-5">
        <div class="row g-5">
            <div class="col-lg-6 wow fadeInLeft" data-wow-delay="0.1s">
                <div class="contact-item">
                    <div class="pb-5">
                        <h4 class="text-primary">Contáctanos</h4>
                        <h1 class="display-4 mb-4">Tu éxito, nuestro próximo proyecto. ¡Contáctanos!</h1>
                        <p><b>¿Listo para empezar tu proyecto? ¿Tienes preguntas o necesitas una cita?</b></p>
                        <p class="mb-0">
                            Usa el siguiente formulario para contactarnos. Proporciónanos tu nombre, número de teléfono, correo electrónico, el asunto de tu mensaje y cuéntanos sobre lo que necesitas. Te responderemos a la brevedad.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3s">
                <form action="{{ route('contact.message') }}" method="POST">
                    @csrf

                    {{-- Bloque para mostrar todos los errores de validación --}}
                    {{-- @if ($errors->any())
                        <div class="alert alert-danger mb-4">
                            <ul>
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif --}}

                    <div class="row g-3">
                        <div class="col-lg-12 col-xl-6">
                            <div class="form-floating">
                                <input type="text" class="form-control @error('full_name') is-invalid @enderror" id="full_name" name="full_name" pattern="[A-Za-z ]+" oninput="this.value = this.value.replace(/[^a-zA-ZñÑáéíóúÁÉÍÓÚ\s]/g, '');" placeholder="Nombre y apellido"
                                value="{{ Auth::check() ? Auth::user()->name.' '.Auth::user()->last_name : old('full_name') }}" required>
                                <label for="full_name">Nombre y apellido *</label>
                                @error('full_name')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-12 col-xl-6">
                            <div class="form-floating">
                                <input type="text" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" inputmode="numeric" pattern="[0-9]{8}" maxlength="8" oninput="this.value = this.value.replace(/[^0-9]/g, '');" placeholder="Teléfono" 
                                value="{{ Auth::check() ? Auth::user()->phone : old('phone') }}">
                                <label for="phone">Teléfono</label>
                                @error('phone')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="form-floating">
                                <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" placeholder="Email"
                                value="{{ Auth::check() ? Auth::user()->email : old('email') }}" required>
                                <label for="email">Correo electrónico *</label>
                                @error('email')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-floating">
                                <input type="text" class="form-control @error('subject') is-invalid @enderror" id="subject" name="subject" placeholder="Asunto" value="{{ old('subject') }}" required>
                                <label for="subject">Asunto *</label>
                                @error('subject')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-floating">
                                <textarea class="form-control @error('message') is-invalid @enderror" placeholder="Detalla tu mensaje" id="message" name="message" style="height: 160px" required>{{ old('message') }}</textarea>
                                <label for="message">Escribe tu mensaje *</label>
                                @error('message')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-12">
                            <button class="btn btn-primary w-100 py-3" type="submit">Enviar mensaje</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <br><br><br>
        <div class="row">
            <div class="row wow fadeInUp" data-wow-delay="0.1s">
                <div class="col-md-4 wow fadeInUp" data-wow-delay="0.2s">
                    <div class="d-flex align-items-center mb-4">
                        <div class="bg-primary btn-lg-square rounded-circle p-4">
                            <i class="fa fa-home fa-2x text-white"></i>
                        </div>
                        <div class="ms-4">
                            <h4>Ubicación</h4>
                            <div class="d-flex flex-wrap">
                                <a href="https://ul.waze.com/ul?place=ChIJ1THxqtgxY48R3mAUX2XNA2M&ll=13.66434690%2C-89.24661490&navigate=yes&utm_campaign=default&utm_source=waze_website&utm_medium=lm_share_location"
                                target="_blank"
                                rel="noopener noreferrer"
                                class="btn btn-primary me-2 mb-2">
                                    Abrir en Waze
                                </a>
                                <a href="https://www.google.com/maps/dir/?api=1&destination=13.66434690,-89.24661490&destination_place_id=ChIJ1THxqtgxY48R3mAUX2XNA2M"
                                target="_blank"
                                rel="noopener noreferrer"
                                class="btn btn-primary mb-2">
                                    Abrir en Google Maps
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-4">
                        <div class="bg-primary btn-lg-square rounded-circle p-2">
                            <i class="fa fa-phone-alt fa-2x text-white"></i>
                        </div>
                        <div class="ms-4">
                            <a href="tel:+50322622861">
                                <h4>Llamanos!</h4>
                            </a>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-4">
                        <div class="bg-primary btn-lg-square rounded-circle p-2">
                            <i class="fab fa-whatsapp fa-2x text-white"></i>
                        </div>
                        <div class="ms-4">
                            <a href="https://wa.me/50363105831?text=Hola,%20estoy%20interesado%20en%20los%20servicios%20que%20ofrecen.%20%C2%BFPodr%C3%ADamos%20coordinar%20una%20reuni%C3%B3n%20para%20conversar%20m%C3%A1s%20al%20respecto%3F">
                                <h4>Escríbenos en WhatsApp!</h4>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 wow fadeInUp" data-wow-delay="0.2s">
                    <div class="fb-page" data-href="https://www.facebook.com/avanceydesempeno" data-tabs="timeline"
                        data-width="400" data-height="630"
                        data-small-header="false"
                        data-adapt-container-width="true"
                        data-hide-cover="false"
                        data-show-facepile="true" style="justify-content: center;"  alig="center" center>
                        <blockquote cite="https://www.facebook.com/avanceydesempeno" class="fb-xfbml-parse-ignore">
                            <a href="https://www.facebook.com/avanceydesempeno">
                                Avance y Desempeño
                            </a>
                        </blockquote>
                    </div>
                </div>
                <div class="col-md-4 wow fadeInUp" data-wow-delay="0.2s" style="justify-content: center;"  alig="center" center>
                    <a href="https://www.linkedin.com/company/consultores-ayd/" target="_blank"><img src="{{ asset('img/aydlinkedin.png') }}" alt="" class="img-fluid" style="height: 630px;"></a>
                </div>
            </div>
        </div>
        <br><br>
        <div class="row">
            <div class="col-12 wow fadeInUp" data-wow-delay="0.1s">
                <div class="rounded h-100">
                    <iframe class="rounded-top w-100"
                    style="height: 500px; margin-bottom: -6px;"
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d4886.4989521645575!2d-89.24918982399396!3d13.664346886718453!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x8f6331d8aaf131d5%3A0x6303cd655f1460de!2sAvance%20y%20Desempe%C3%B1o!5e1!3m2!1ses-419!2ssv!4v1751094914356!5m2!1ses-419!2ssv"
                    loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                    <div class="d-flex align-items-center justify-content-center bg-primary rounded-bottom p-4">
                        <div class="d-flex">
                            <a class="btn btn-dark btn-lg-square me-2" href="https://www.facebook.com/avanceydesempeno/" target="_blank"><i class="fab fa-facebook-f fa-2x"></i></a>
                            <a class="btn btn-dark btn-lg-square mx-2" href="https://www.youtube.com/channel/UCYESjdd-4RDBJlgZxTj4MxA" target="_blank"><i class="fab fa-youtube fa-2x"></i></a>
                            <a class="btn btn-dark btn-lg-square mx-2" href="https://www.instagram.com/avanceydesempeno/" target="_blank"><i class="fab fa-instagram fa-2x"></i></a>
                            <a class="btn btn-dark btn-lg-square mx-2" href="https://x.com/AvanceDesempeno" target="_blank"><img src="{{ asset('img/x.svg') }}" class="white-svg-icon" alt="X Logo" style="width: 25px;"></a>
                            <a class="btn btn-dark btn-lg-square mx-2" href="https://www.linkedin.com/company/consultores-ayd/?originalSubdomain=sv" target="_blank"><i class="fab fa-linkedin-in fa-2x"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{-- <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        @if ($errors->any())
            Swal.fire({
                icon: 'error',
                title: 'Error de Validación',
                text: 'Ocurrió un error en la validación de datos. Por favor, revisa nuevamente el formulario y corrige los campos resaltados.',
                confirmButtonText: 'Entendido'
            });
        @endif
    });
</script> --}}