<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test CV Upload</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* CV Upload Styles */
        .cv-upload-container {
            border: 2px dashed #e1e5e9;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            background: #fafbfc;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .cv-upload-container:hover {
            border-color: #007bff;
            background: #f8f9ff;
        }

        .cv-upload-container.dragover {
            border-color: #007bff;
            background: #e3f2fd;
            transform: scale(1.02);
        }

        .cv-upload-container.error {
            border-color: #dc3545;
            background: #fff5f5;
        }

        .cv-upload-content {
            pointer-events: none;
        }

        .cv-upload-icon {
            color: #6c757d;
            margin-bottom: 1rem;
        }

        .cv-upload-container:hover .cv-upload-icon {
            color: #007bff;
        }

        .cv-upload-text h6 {
            color: #495057;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .cv-browse-link {
            color: #007bff;
            text-decoration: underline;
            cursor: pointer;
        }

        .cv-browse-link:hover {
            color: #0056b3;
        }

        /* Preview Styles */
        .cv-preview-container {
            margin-top: 1rem;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }

        .cv-preview-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background: #f8f9fa;
            border-bottom: 1px solid #e1e5e9;
        }

        .cv-file-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .cv-file-icon {
            color: #dc3545;
            display: flex;
            align-items: center;
        }

        .cv-file-details {
            display: flex;
            flex-direction: column;
        }

        .cv-file-name {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
        }

        .cv-file-size {
            font-size: 0.8rem;
            color: #6c757d;
        }

        .cv-remove-btn {
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .cv-remove-btn:hover {
            background: #e9ecef;
            color: #dc3545;
        }

        .cv-pdf-viewer {
            background: #f8f9fa;
        }

        .cv-pdf-viewer iframe {
            border: none;
            background: white;
        }

        /* Progress Styles */
        .cv-progress-container {
            margin-top: 1rem;
        }

        .cv-progress-bar {
            width: 100%;
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
        }

        .cv-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            width: 0%;
            transition: width 0.3s ease;
        }

        .cv-progress-text {
            display: block;
            margin-top: 0.5rem;
            color: #6c757d;
            text-align: center;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .cv-upload-container {
                padding: 1.5rem 1rem;
            }
            
            .cv-pdf-viewer iframe {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <h2 class="mb-4">Test CV Upload Component</h2>
                
                <form>
                    <div class="mb-3">
                        <label for="cv" class="form-label">Sube tu curriculum vitae: <span class="text-danger">*</span></label>
                        
                        <!-- Drag & Drop Area -->
                        <div id="cv-upload-area" class="cv-upload-container">
                            <div class="cv-upload-content">
                                <div class="cv-upload-icon">
                                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                        <polyline points="14,2 14,8 20,8"></polyline>
                                        <line x1="16" y1="13" x2="8" y2="13"></line>
                                        <line x1="16" y1="17" x2="8" y2="17"></line>
                                        <polyline points="10,9 9,9 8,9"></polyline>
                                    </svg>
                                </div>
                                <div class="cv-upload-text">
                                    <h6 class="mb-1">Arrastra tu CV aquí</h6>
                                    <p class="text-muted mb-2">o <span class="cv-browse-link">busca en tu dispositivo</span></p>
                                    <small class="text-muted">Solo archivos PDF • Máximo 5MB</small>
                                </div>
                            </div>
                            
                            <!-- Hidden File Input -->
                            <input type="file" 
                                   class="cv-file-input" 
                                   id="cv" 
                                   name="cv" 
                                   accept=".pdf,application/pdf" 
                                   hidden>
                        </div>
                        
                        <!-- File Preview -->
                        <div id="cv-preview" class="cv-preview-container" style="display: none;">
                            <div class="cv-preview-header">
                                <div class="cv-file-info">
                                    <div class="cv-file-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                            <polyline points="14,2 14,8 20,8"></polyline>
                                        </svg>
                                    </div>
                                    <div class="cv-file-details">
                                        <div class="cv-file-name"></div>
                                        <div class="cv-file-size"></div>
                                    </div>
                                </div>
                                <button type="button" class="cv-remove-btn" title="Eliminar archivo">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <line x1="18" y1="6" x2="6" y2="18"></line>
                                        <line x1="6" y1="6" x2="18" y2="18"></line>
                                    </svg>
                                </button>
                            </div>
                            
                            <!-- PDF Viewer -->
                            <div class="cv-pdf-viewer">
                                <iframe id="cv-iframe" 
                                        src="" 
                                        frameborder="0" 
                                        width="100%" 
                                        height="400">
                                </iframe>
                            </div>
                        </div>
                        
                        <!-- Upload Progress -->
                        <div id="cv-progress" class="cv-progress-container" style="display: none;">
                            <div class="cv-progress-bar">
                                <div class="cv-progress-fill"></div>
                            </div>
                            <small class="cv-progress-text">Cargando archivo...</small>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Enviar</button>
                </form>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Initializing CV upload functionality...');

        const uploadArea = document.getElementById('cv-upload-area');
        const fileInput = document.getElementById('cv');
        const previewContainer = document.getElementById('cv-preview');
        const progressContainer = document.getElementById('cv-progress');
        const progressFill = document.querySelector('.cv-progress-fill');
        const iframe = document.getElementById('cv-iframe');

        console.log('Elements found:', {
            uploadArea: !!uploadArea,
            fileInput: !!fileInput,
            previewContainer: !!previewContainer,
            progressContainer: !!progressContainer,
            progressFill: !!progressFill,
            iframe: !!iframe
        });

        if (!uploadArea || !fileInput) {
            console.error('Required elements not found');
            return;
        }

        // Click to browse
        uploadArea.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Upload area clicked');
            fileInput.click();
        });

        // Drag and drop events
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.stopPropagation();
            uploadArea.classList.add('dragover');
            console.log('Drag over');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (!uploadArea.contains(e.relatedTarget)) {
                uploadArea.classList.remove('dragover');
                console.log('Drag leave');
            }
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            e.stopPropagation();
            uploadArea.classList.remove('dragover');
            console.log('File dropped');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            console.log('File input changed');
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        // Remove file button
        document.addEventListener('click', (e) => {
            if (e.target.closest('.cv-remove-btn')) {
                console.log('Remove button clicked');
                removeFile();
            }
        });

        function handleFile(file) {
            console.log('Handling file:', file.name, file.type, file.size);

            // Validate file type
            if (file.type !== 'application/pdf' && !file.name.toLowerCase().endsWith('.pdf')) {
                showError('Solo se permiten archivos PDF');
                return;
            }

            // Validate file size (5MB)
            if (file.size > 5 * 1024 * 1024) {
                showError('El archivo no puede ser mayor a 5MB');
                return;
            }

            // Clear any previous errors
            clearError();

            // Show progress
            showProgress();

            // Simulate upload progress
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 30;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    setTimeout(() => {
                        hideProgress();
                        showPreview(file);
                    }, 500);
                }
                if (progressFill) {
                    progressFill.style.width = progress + '%';
                }
            }, 100);

            // Update file input
            const dt = new DataTransfer();
            dt.items.add(file);
            fileInput.files = dt.files;
        }

        function showPreview(file) {
            console.log('Showing preview for:', file.name);

            // Hide upload area
            uploadArea.style.display = 'none';

            // Show preview
            if (previewContainer) {
                previewContainer.style.display = 'block';
            }

            // Update file info
            const fileName = document.querySelector('.cv-file-name');
            const fileSize = document.querySelector('.cv-file-size');

            if (fileName) fileName.textContent = file.name;
            if (fileSize) fileSize.textContent = formatFileSize(file.size);

            // Create object URL for PDF preview
            const fileURL = URL.createObjectURL(file);
            if (iframe) {
                iframe.src = fileURL;
            }
        }

        function removeFile() {
            console.log('Removing file');

            // Clear file input
            fileInput.value = '';

            // Hide preview
            if (previewContainer) {
                previewContainer.style.display = 'none';
            }

            // Show upload area
            uploadArea.style.display = 'block';

            // Clear iframe
            if (iframe) {
                iframe.src = '';
            }

            // Clear any errors
            clearError();
        }

        function showProgress() {
            if (progressContainer) {
                progressContainer.style.display = 'block';
            }
            if (progressFill) {
                progressFill.style.width = '0%';
            }
        }

        function hideProgress() {
            if (progressContainer) {
                progressContainer.style.display = 'none';
            }
        }

        function showError(message) {
            console.log('Showing error:', message);
            uploadArea.classList.add('error');

            // Remove existing error messages
            const existingError = document.querySelector('.cv-error-message');
            if (existingError) {
                existingError.remove();
            }

            // Add error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'cv-error-message alert alert-danger mt-2';
            errorDiv.textContent = message;
            uploadArea.parentNode.appendChild(errorDiv);
        }

        function clearError() {
            uploadArea.classList.remove('error');
            const errorMessage = document.querySelector('.cv-error-message');
            if (errorMessage) {
                errorMessage.remove();
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    });
    </script>
</body>
</html>
