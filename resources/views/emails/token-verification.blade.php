<!doctype html>
<html lang="es">
<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>Avance y Desempeño - Código de Verificación</title>
	<style media="all" type="text/css">
	/* -------------------------------------
	GLOBAL RESETS
------------------------------------- */
	.avanceyd-principal-color{
			color: #17385A;
		}
		
		img {
		border: 0;
		-ms-interpolation-mode: bicubic;
	}

	body {
		font-family: Helvetica, sans-serif;
		-webkit-font-smoothing: antialiased;
		font-size: 16px;
		line-height: 1.3;
		-ms-text-size-adjust: 100%;
		-webkit-text-size-adjust: 100%;
	}
	
	table {
		border-collapse: separate;
		mso-table-lspace: 0pt;
		mso-table-rspace: 0pt;
		width: 100%;
	}
	
	table td {
		font-family: Helvetica, sans-serif;
		font-size: 16px;
		vertical-align: top;
	}
	/* -------------------------------------
	BODY & CONTAINER
------------------------------------- */
	
	body {
		background-color: #f4f5f6;
		margin: 0;
		padding: 0;
	}
	
	.body {
		background-color: #f4f5f6;
		width: 100%;
	}
	
	.container {
		margin: 0 auto !important;
		max-width: 600px;
		padding: 0;
		padding-top: 24px;
		width: 600px;
	}
	
	.content {
		box-sizing: border-box;
		display: block;
		margin: 0 auto;
		max-width: 600px;
		padding: 0;
	}
	/* -------------------------------------
	HEADER, FOOTER, MAIN
------------------------------------- */
	
	.main {
		background: #ffffff;
		border: 1px solid #eaebed;
		border-radius: 16px;
		width: 100%;
	}
	
	.wrapper {
		box-sizing: border-box;
		padding: 18px;
	}
	
	.footer {
		clear: both;
		padding-top: 24px;
		text-align: center;
		width: 100%;
	}
	
	.footer td,
	.footer p,
	.footer span,
	.footer a {
		color: #9a9ea6;
		font-size: 16px;
		text-align: center;
	}
	/* -------------------------------------
	TYPOGRAPHY
------------------------------------- */
	
	p {
		font-family: Helvetica, sans-serif;
		font-size: 16px;
		font-weight: normal;
		margin: 0;
		margin-bottom: 16px;
	}
	
	a {
		color: #0867ec;
		text-decoration: underline;
	}
	/* -------------------------------------
	BUTTONS
------------------------------------- */
	
	.btn {
		box-sizing: border-box;
		min-width: 100% !important;
		width: 100%;
	}
	
	.btn > tbody > tr > td {
		padding-bottom: 16px;
	}
	
	.btn table {
		width: auto;
	}
	
	.btn table td {
		background-color: #ffffff;
		border-radius: 4px;
		text-align: center;
	}
	
	.btn a {
		background-color: #000;
		border: solid 2px #fc7417;
		border-radius: 4px;
		box-sizing: border-box;
		color: #fc7417;
		cursor: pointer;
		display: inline-block;
		font-size: 14px;
		font-weight: bold;
		margin: 0;
		padding: 11px 24px;
		text-decoration: none;
		text-transform: capitalize;
	}
	
	.btn-primary table td {
		background-color: #17385A;
	}
	
	.btn-primary a {
		background-color: #17385A;
		border-color: #17385A;
		color: #ffffff;
	}
	
	@media all {
		.btn-primary table td:hover {
			background-color: #17385A !important;
		}
		.btn-primary a:hover {
			background-color: #17385A !important;
			border-color: #17385A !important;
		}
	}
	
	/* -------------------------------------
	OTHER STYLES THAT MIGHT BE USEFUL
------------------------------------- */
	
	.last {
		margin-bottom: 0;
	}
	
	.first {
		margin-top: 0;
	}
	
	.align-center {
		text-align: center;
	}
	
	.align-right {
		text-align: right;
	}
	
	.align-left {
		text-align: left;
	}
	
	.text-link {
		color: #c45910 !important;
		text-decoration: underline !important;
	}
	
	.clear {
		clear: both;
	}
	
	.mt0 {
		margin-top: 0;
	}
	
	.mb0 {
		margin-bottom: 0;
	}
	
	.preheader {
		color: transparent;
		display: none;
		height: 0;
		max-height: 0;
		max-width: 0;
		opacity: 0;
		overflow: hidden;
		mso-hide: all;
		visibility: hidden;
		width: 0;
	}
	
	.powered-by a {
	text-decoration: none;
	}
	
	/* -------------------------------------
	RESPONSIVE AND MOBILE FRIENDLY STYLES
------------------------------------- */
	
	@media only screen and (max-width: 640px) {
	.main p,
	.main td,
	.main span {
		font-size: 16px !important;
	}
	.wrapper {
		padding: 8px !important;
	}
	.content {
		padding: 0 !important;
	}
	.container {
		padding: 0 !important;
		padding-top: 8px !important;
		width: 100% !important;
	}
	.main {
		border-left-width: 0 !important;
		border-radius: 0 !important;
		border-right-width: 0 !important;
	}
	.btn table {
		max-width: 100% !important;
		width: 100% !important;
	}
	.btn a {
		font-size: 16px !important;
		max-width: 100% !important;
		width: 100% !important;
	}
	}
	/* -------------------------------------
	PRESERVE THESE STYLES IN THE HEAD
------------------------------------- */
	
	@media all {
	.ExternalClass {
		width: 100%;
	}
	.ExternalClass,
	.ExternalClass p,
	.ExternalClass span,
	.ExternalClass font,
	.ExternalClass td,
	.ExternalClass div {
		line-height: 100%;
	}
	.apple-link a {
		color: inherit !important;
		font-family: inherit !important;
		font-size: inherit !important;
		font-weight: inherit !important;
		line-height: inherit !important;
		text-decoration: none !important;
	}
	#MessageViewBody a {
		color: inherit;
		text-decoration: none;
		font-size: inherit;
		font-family: inherit;
		font-weight: inherit;
		line-height: inherit;
	}
	}
	</style>
</head>
<body>
	<table role="presentation" border="0" cellpadding="0" cellspacing="0" class="body">
	<tr>
		<td>&nbsp;</td>
		<td class="container">
		<div class="content">

			<!-- START CENTERED WHITE CONTAINER -->
			<span class="preheader">Bienvenido a Avance y Desempeño.</span>
			<table role="presentation" border="0" cellpadding="0" cellspacing="0" class="main">

			<!-- START MAIN CONTENT AREA -->
			<tr>
				<td center align="center">
					<br>
                    <img style="width: 350px;" src="https://avanceydesempeno.com/img/ayd/aydlogo.png" alt="">
				</td>
			</tr>
			<tr>
				<td class="wrapper">
				<br>
                <p style="text-align: center;"><b>¡Mensaje de seguridad!</b></p>
                <p>Por favor, utiliza el siguiente código de verificación para completar el proceso:</p>
				<table role="presentation" border="0" cellpadding="0" cellspacing="0" class="btn btn-primary">
					<tbody>
						<tr>
							<td align="center">
								<table role="presentation" border="0" cellpadding="0" cellspacing="0">
									<tbody>
										<tr>
											<td>
												<h3 style="background-color: #17385A; color: #ffffff; padding: 10px; border-radius: 5px; text-align: center;">{{ $token }}</h3>
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
					</tbody>
				</table>
					<p>Este código es único y válido por 15 minutos, si expira, puedes solicitar otro.</p><br>
                    <p>Si no solicitaste este código, puedes ignorar este correo.</p><br><br>
					<p style="text-align: center;">¡Estamos innovando para ti!</p>
				</td>
			</tr>
			<!-- END MAIN CONTENT AREA -->
			</table>

			<!-- START FOOTER -->
            <div class="footer">
				<table role="presentation" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="content-block">
							{{-- <span class="apple-link">CLIQZA</span> --}}
						{{-- <span class="apple-link">El Salvador</span> --}}
						</td>
					</tr>
				</table>
			</div>
  
			  <!-- END FOOTER -->
			
<!-- END CENTERED WHITE CONTAINER --></div>
		</td>
		<td>&nbsp;</td>
	</tr>
	</table>
</body>
</html>