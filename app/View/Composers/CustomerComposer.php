<?php

namespace App\View\Composers;
use Illuminate\View\View;
use App\Models\WebSectionImage;
use App\Models\WebSection;

class CustomerComposer
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        //
    }
    public function compose(View $view)
    {
        // //obteniendo la informacion
        // $clientes = OurCustomer::first();
        // $clientesImages = OurCustomerImage::all();

        // //funciona para dividir la colección de imagenes
        // $clientesImagesRow1 = $clientesImages->take(40);
        // $clientesImagesRow2 = $clientesImages->skip(40);
        
        // // variables a la vista
        // $view->with([
        //     'clientes' => $clientes,
        //     'clientesImages' => $clientesImages,
        //     'clientesImagesRow1' => $clientesImagesRow1,
        //     'clientesImagesRow2' => $clientesImagesRow2
        // ]);


        // Obtener todas las imágenes usando el nuevo modelo
        $clientes = WebSection::where('name', 'Clientes')->first();
        $clientesImages = WebSectionImage::where('section_id', $clientes->id)->get();

        // Contar el total de imágenes
        $totalImages = $clientesImages->count();
        $half = ceil($totalImages / 2);

        // Dividir la colección en dos filas
        $clientesImagesRow1 = $clientesImages->take($half);
        $clientesImagesRow2 = $clientesImages->skip($half);

        // Pasar las variables a la vista
        $view->with([
            'clientes' => $clientes,
            'clientesImages' => $clientesImages,
            'clientesImagesRow1' => $clientesImagesRow1,
            'clientesImagesRow2' => $clientesImagesRow2
        ]);
    }
}
