<?php

namespace App\View\Composers;
use App\Models\WebSection;
use App\Models\WebPage;
use Illuminate\View\View;

class SubscriptionComposer
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        
    }

    public function compose(View $view)
    {
        //obteniendo la informacion
        $subscribe = WebSection::where('name', 'Boletín')
        ->whereHas('page', function ($query) {
            $query->where('name', 'Master');
        })
        ->first();
    
        // variables a la vista
        $view->with([
            'subscribe' => $subscribe,
        ]);
    }
}
