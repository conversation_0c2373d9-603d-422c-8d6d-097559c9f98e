<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class LogUserActivity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Log user activity
        $this->logActivity($request, $response);

        return $response;
    }

    /**
     * Log user activity details.
     */
    private function logActivity(Request $request, Response $response): void
    {
        try {
            $user = Auth::user();
            $userId = $user ? $user->id : null;
            $userName = $user ? $user->name : 'Guest';

            $logData = [
                'timestamp' => now()->toISOString(),
                'user_id' => $userId,
                'user_name' => $userName,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'method' => $request->method(),
                'url' => $request->fullUrl(),
                'route' => $request->route() ? $request->route()->getName() : null,
                'status_code' => $response->getStatusCode(),
                'referer' => $request->header('referer'),
                'session_id' => $request->session()->getId(),
            ];

            // Add request data for specific routes
            if ($this->shouldLogRequestData($request)) {
                $logData['request_data'] = $this->sanitizeRequestData($request->all());
            }

            // Detect suspicious activity
            if ($this->isSuspiciousActivity($request, $response)) {
                Log::channel('security')->warning('Suspicious activity detected', $logData);
            }

            // Log normal activity
            Log::channel('user_activity')->info('User activity', $logData);

        } catch (\Exception $e) {
            Log::channel('errors')->error('Error logging user activity', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Determine if request data should be logged.
     */
    private function shouldLogRequestData(Request $request): bool
    {
        $routesToLog = [
            'contact.store',
            'newsletter.subscribe',
            'login',
            'register',
        ];

        $routeName = $request->route() ? $request->route()->getName() : null;
        
        return in_array($routeName, $routesToLog) || 
               $request->isMethod('POST') || 
               $request->isMethod('PUT') || 
               $request->isMethod('DELETE');
    }

    /**
     * Sanitize request data to remove sensitive information.
     */
    private function sanitizeRequestData(array $data): array
    {
        $sensitiveFields = ['password', 'password_confirmation', 'token', '_token', 'api_key'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }

        return $data;
    }

    /**
     * Detect suspicious activity patterns.
     */
    private function isSuspiciousActivity(Request $request, Response $response): bool
    {
        // Check for common attack patterns
        $suspiciousPatterns = [
            'sql injection' => ['union', 'select', 'drop', 'insert', 'update', 'delete', '--', ';'],
            'xss' => ['<script', 'javascript:', 'onload=', 'onerror='],
            'path traversal' => ['../', '..\\', '/etc/passwd', '/etc/shadow'],
            'command injection' => ['|', '&&', ';', '`', '$(',],
        ];

        $requestString = strtolower($request->getContent() . $request->getQueryString());

        foreach ($suspiciousPatterns as $type => $patterns) {
            foreach ($patterns as $pattern) {
                if (strpos($requestString, $pattern) !== false) {
                    return true;
                }
            }
        }

        // Check for unusual status codes
        if (in_array($response->getStatusCode(), [403, 404, 429, 500, 503])) {
            return true;
        }

        // Check for too many requests from same IP
        $ip = $request->ip();
        $cacheKey = "suspicious_activity_{$ip}";
        $requestCount = cache()->get($cacheKey, 0);
        
        if ($requestCount > 100) { // More than 100 requests per minute
            return true;
        }

        cache()->put($cacheKey, $requestCount + 1, 60); // Cache for 1 minute

        return false;
    }
}
