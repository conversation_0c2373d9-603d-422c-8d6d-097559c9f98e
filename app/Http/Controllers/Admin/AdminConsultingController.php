<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\WebPage;
use App\Models\WebSection;
use App\Models\WebSubSection;
use App\Models\WebTypeSection;
use App\Models\HeaderPage;
use App\Models\Status;
use App\Models\CorporateValue;
use App\Models\BannerSection;
use Illuminate\Support\Str;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Support\Facades\DB;
use App\Models\WebSubSectionImage;

class AdminConsultingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $consultoriasPage = WebPage::where('name', 'Consultorias')->first();
        $banners = BannerSection::orderBy('order')->get();
        $secciones = WebSection::where('page_id', $consultoriasPage->id)->get();
        $detalleConsultorias = WebSubSection::where('section_id', 5)->get();
        return view('admin.pages.consulting.index', compact('banners', 'secciones', 'detalleConsultorias'));
    }

    //ver sección para editar
    public function viewSection($id)
    {
        $section = WebSection::with('subsections')->findOrFail($id);
        return view('admin.pages.consulting.sections', compact('section'));
    }

    //detalles de consultorias
    public function viewDetails($id)
    {
        $subsection = WebSubSection::with('images')->findOrFail($id);
        return view('admin.pages.consulting.details', compact('subsection'));
    }

    //para crear nueva consultoria
    public function createNewDetail()
    {
        return view('admin.pages.consulting.create');
    }

    //guardar nueva consultoria
    public function storeDetails(Request $request)
    {
        $consultoriasPage = WebSection::where('name', 'Consultorías')->first();
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'subsection_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5000',
        ]);
        try {
            $subsection = WebSubSection::create([
                'name' => "Detalles de consultorías",
                'description' => $request->title,
                'title' => $request->title,
                'content' => $request->content,
                'section_id' => $consultoriasPage->id,
                'status_id' => 1,
                'order' => 1,
                'button' => false,
            ]);
            if ($request->hasFile('subsection_images')) {
                foreach ($request->file('subsection_images') as $image) {
                    $imageExtension = $image->getClientOriginalExtension();
                    $imageFilename = Str::random(20) . '.' . $imageExtension;
                    $image->move(public_path('img/consultorias'), $imageFilename);
                    WebSubSectionImage::create([
                        'subsection_id' => $subsection->id,
                        'image' => 'consultorias/' . $imageFilename,
                        'alt' => $request->title,
                    ]);
                }
            }
            Alert::toast('Nuevo detalle de consultoría creado exitosamente.', 'success');
            return redirect()->route('admin.pages.consulting');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al crear el detalle. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //actualizar detalles de consultorias
    public function updateDetails(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'subsection_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5000',
        ]);
        try {
            $subsection = WebSubSection::findOrFail($id);
            $subsection->update([
                'title' => $request->title,
                'content' => $request->content,
            ]);
            //subida de múltiples imágenes para la galería
            if ($request->hasFile('subsection_images')) {
                foreach ($request->file('subsection_images') as $image) {
                    $imageExtension = $image->getClientOriginalExtension();
                    $imageFilename = Str::random(20) . '.' . $imageExtension;
                    //carpeta de galería
                    $image->move(public_path('img/consultorias'), $imageFilename);
                    WebSubSectionImage::create([
                        'subsection_id' => $subsection->id,
                        'image' => 'img/consultorias/' . $imageFilename,
                        'alt' => $request->title,
                    ]);
                }
            }
            Alert::toast('Información actualizada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al actualizar la subsección. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //eliminar consultoria con imagenes
    public function deleteDetail($id)
    {
        try {
            $subsection = WebSubSection::with('images')->findOrFail($id);
            foreach ($subsection->images as $image) {
                //eliminar de la carpeta/servidor
                if (file_exists(public_path($image->image))) {
                    unlink(public_path($image->image));
                }
                //eliminar de la base de datos
                $image->delete();
            }
            $subsection->delete();
            Alert::toast('Consultoría eliminada exitosamente.', 'success');
            return redirect()->route('admin.pages.consulting');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al eliminar la consultoría. Error: ' . $th->getMessage(), 'error');
            return redirect()->back();
        }
    }

    //actualizar sección de consultorias
    public function updateSection(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'sub_title' => 'required|string|max:255',
            'content' => 'required|string',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5000',
        ]);
        try {
            $section = WebSection::findOrFail($id);
            $data = [
                'title' => $request->title,
                'sub_title' => $request->sub_title,
                'content' => $request->content,
            ];
            if ($request->hasFile('cover_image')) {
                if ($section->cover_image && file_exists(public_path($section->cover_image))) {
                    unlink(public_path($section->cover_image));
                }
                $file = $request->file('cover_image');
                $extension = $file->getClientOriginalExtension();
                $filename = Str::random(20) . '.' . $extension;
                $file->move(public_path('img/consultorias'), $filename);
                $data['cover_image'] = 'img/consultorias/'.$filename;
            }
            $section->update($data);
            Alert::toast('Información actualizada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al actualizar la sección. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }
}
