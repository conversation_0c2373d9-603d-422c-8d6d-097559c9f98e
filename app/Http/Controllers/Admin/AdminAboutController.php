<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\WebPage;
use App\Models\WebSection;
use App\Models\WebSubSection;
use App\Models\WebTypeSection;
use App\Models\HeaderPage;
use App\Models\Status;
use App\Models\CorporateValue;
use Illuminate\Support\Str;
use RealRashid\SweetAlert\Facades\Alert;
use App\Models\WebSectionImage;

class AdminAboutController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //nosotros
        $nosotrosPage = WebPage::where('name', 'Nosotros')->first();
        $seccionesNosotros = WebSection::where('page_id', $nosotrosPage->id)->get();
        $nosotros = WebSection::where('name', 'Nuestra empresa')->first();
        //seccion vision y mision
        $seccionVM = WebSection::where('name', 'Visión y Misión')->first();
        //mision
        $mision = WebSubSection::where('name', 'Detalles de Misión')->first();
        //vision
        $vision = WebSubSection::where('name', 'Detalles de Visión')->first();
        //valores
        $valores = CorporateValue::all();
        return view('admin.pages.about.index', compact('seccionesNosotros', 'seccionVM', 'mision', 'vision', 'nosotros', 'valores'));
    }

    //SECCIONES ACERCA DE NOSOTROS
    //ver sección
    public function viewSection($id)
    {
        $section = WebSection::with('subsections')->findOrFail($id);
        return view('admin.pages.about.sections', compact('section'));
    }

    //actualizar sección
    public function updateSection(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'sub_title' => 'required|string|max:255',
            'content' => 'required|string',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5000',
        ]);
        try {
            $section = WebSection::findOrFail($id);
            $data = [
                'title' => $request->title,
                'sub_title' => $request->sub_title,
                'content' => $request->content,
            ];
            if ($request->hasFile('cover_image')) {
                if ($section->cover_image && file_exists(public_path($section->cover_image))) {
                    unlink(public_path($section->cover_image));
                }
                $file = $request->file('cover_image');
                $extension = $file->getClientOriginalExtension();
                $filename = Str::random(20) . '.' . $extension;
                $file->move(public_path('img/about'), $filename);
                $data['cover_image'] = 'img/about/'.$filename;
            }
            $section->update($data);
            Alert::toast('Información actualizada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al actualizar la sección. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }





    //actualizar subsección
    public function updateSubSection(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5000',
        ]);
        try {
            $subsection = WebSubSection::findOrFail($id);
            $data = [
                'title' => $request->title,
                'content' => $request->content,
            ];
            if ($request->hasFile('cover_image')) {
                if ($subsection->cover_image && file_exists(public_path($subsection->cover_image))) {
                    unlink(public_path($subsection->cover_image));
                }
                $file = $request->file('cover_image');
                $extension = $file->getClientOriginalExtension();
                $filename = Str::random(20) . '.' . $extension;
                $file->move(public_path('img/about'), $filename);
                $data['cover_image'] = 'img/about/'.$filename;
            }
            $subsection->update($data);
            Alert::toast('Información actualizada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al actualizar la subsección. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //valores
    //ver valor
    public function viewCorporate($id)
    {
        $valor = CorporateValue::findOrFail($id);
        return view('admin.pages.about.edit-corporate', compact('valor'));
    }

    //actualizar valor
    public function updateCorporate(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'icon' => 'string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg,webp|max:5000',
        ]);
        try {
            $valor = CorporateValue::findOrFail($id);
            $data = [
                'name' => $request->name,
                'description' => $request->description,
                'icon' => $request->icon,
            ];
            if ($request->hasFile('image')) {
                if ($valor->image && file_exists(public_path($valor->image))) {
                    unlink(public_path($valor->image));
                }
                $file = $request->file('image');
                $extension = $file->getClientOriginalExtension();
                $filename = Str::random(20) . '.' . $extension;
                $file->move(public_path('img/about'), $filename);
                $data['image'] = 'img/about/'.$filename;
            }
            $valor->update($data);
            Alert::toast('Información actualizada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al actualizar la información. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }
}
