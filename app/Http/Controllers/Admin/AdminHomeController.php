<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\WebPage;
use App\Models\WebSection;
use App\Models\WebSubSection;
use App\Models\WebTypeSection;
use App\Models\HeaderPage;
use App\Models\Status;
use App\Models\CorporateValue;
use App\Models\BannerSection;
use Illuminate\Support\Str;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Support\Facades\DB;

class AdminHomeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    //INICIO
    public function HomePage()
    {
        $inicioPage = WebPage::where('name', 'Inicio')->first();
        $banners = BannerSection::orderBy('order')->get();
        $secciones = WebSection::where('page_id', $inicioPage->id)->get();
        return view('admin.pages.home.index', compact('banners', 'secciones'));
    }

    //BANNERS

    //ver banner
    public function viewBanner($id)
    {
        $banner = BannerSection::findOrFail($id);
        return view('admin.pages.home.banner', compact('banner'));
    }

    //crear banner
    public function storeBanner(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'string|max:255',
            'img_banner' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5000',
        ]);
        try {
            $maxOrder = BannerSection::max('order');
            $newOrder = $maxOrder !== null ? $maxOrder + 1 : 1;

            $file = $request->file('img_banner');
            $extension = $file->getClientOriginalExtension();
            $filename = Str::random(20) . '.' . $extension;
            $file->move(public_path('img/banner'), $filename);
            BannerSection::create([
                'title' => $request->title,
                'content' => $request->content,
                'img_banner' => 'img/banner/'.$filename,
                'order' => $newOrder,
                'button' => false,
            ]);
            Alert::toast('Banner creado exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al crear el banner. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //actualizar banner
    public function updateBanner(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'string|max:255',
            'img_banner' => 'image|mimes:jpeg,png,jpg,gif,webp|max:5000',
        ]);
        try {
            $banner = BannerSection::findOrFail($id);
            $data = [
                'title' => $request->title,
                'content' => $request->content,
            ];
        if ($request->hasFile('img_banner')) {
            if ($banner->img_banner && file_exists(public_path($banner->img_banner))) {
                unlink(public_path($banner->img_banner));
            }
            $file = $request->file('img_banner');
            $extension = $file->getClientOriginalExtension();
            $filename = Str::random(20) . '.' . $extension;
            $file->move(public_path('img/banner'), $filename);
            $data['img_banner'] = 'img/banner/'.$filename;
        }
            $banner->update($data);
            Alert::toast('Información actualizada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al actualizar el banner. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //eliminar banner
    public function deleteBanner($id)
    {
        try {
            $banner = BannerSection::findOrFail($id);
            if ($banner->img_banner && file_exists(public_path($banner->img_banner))) {
                unlink(public_path($banner->img_banner));
            }
            $banner->delete();
            Alert::toast('Banner eliminado exitosamente.', 'success');
            return redirect()->route('admin.pages.home');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al eliminar el banner. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    public function updateOrderBanner(Request $request)
    {
        $request->validate([
            'banner_order' => 'required|array',
            'banner_order.*' => 'exists:banner_sections,id',
        ]);
        try {
            DB::beginTransaction();
            $order = 1;
            foreach ($request->input('banner_order') as $bannerId) {
                BannerSection::where('id', $bannerId)->update(['order' => $order]);
                $order++;
            }
            DB::commit();
            return response()->json(['message' => 'Orden de banners actualizado exitosamente.']);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(['error' => 'Ocurrió un error al actualizar el orden.'], 500);
        }
    }
    // FIN BANNERS

    //SECCIONES INICIO
    //ver sección
    public function viewSection($id)
    {
        $section = WebSection::with('subsections')->findOrFail($id);
        return view('admin.pages.home.sections', compact('section'));
    }

    //actualizar sección
    public function updateSection(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'sub_title' => 'required|string|max:255',
            'content' => 'required|string',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5000',
        ]);
        try {
            $section = WebSection::findOrFail($id);
            $data = [
                'title' => $request->title,
                'sub_title' => $request->sub_title,
                'content' => $request->content,
            ];
            if ($request->hasFile('cover_image')) {
                if ($section->cover_image && file_exists(public_path($section->cover_image))) {
                    unlink(public_path($section->cover_image));
                }
                $file = $request->file('cover_image');
                $extension = $file->getClientOriginalExtension();
                $filename = Str::random(20) . '.' . $extension;
                $file->move(public_path('img/home/'), $filename);
                $data['cover_image'] = 'img/home/'.$filename;
            }
            $section->update($data);
            Alert::toast('Información actualizada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al actualizar la sección. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //actualizar subsección
    public function updateSubSection(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5000',
        ]);
        try {
            $subsection = WebSubSection::findOrFail($id);
            $data = [
                'title' => $request->title,
                'content' => $request->content,
            ];
            if ($request->hasFile('cover_image')) {
                if ($subsection->cover_image && file_exists(public_path($subsection->cover_image))) {
                    unlink(public_path($subsection->cover_image));
                }
                $file = $request->file('cover_image');
                $extension = $file->getClientOriginalExtension();
                $filename = Str::random(20) . '.' . $extension;
                $file->move(public_path('img/home/'), $filename);
                $data['cover_image'] = 'img/home/'.$filename;
            }
            $subsection->update($data);
            Alert::toast('Información actualizada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al actualizar la subsección. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    
}
