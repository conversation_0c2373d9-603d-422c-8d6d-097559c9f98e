<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Professional;
use Illuminate\Http\Request;
use App\Models\WebPage;
use App\Models\WebSection;
use App\Models\WebSubSection;
use App\Models\WebSubSectionImage;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Support\Str;

class AdminProfessionalController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // $professionalPage = WebPage::where('name', 'Formación Profesional')->first();
        $profesional = Professional::all();
        return view('admin.pages.professional.index', compact('profesional'));
    }

    //vista para crear formación profesional
    public function create()
    {
        return view('admin.pages.professional.create');
    }

    //vista para editar formación profesional
    public function viewDetails($id)
    {
        $professional = Professional::findOrFail($id);
        return view('admin.pages.professional.details', compact('professional'));
    }

    //guardar nueva formación profesional
    public function storeProfessional(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'content' => 'required|string',
            // 'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg,webp|max:5000',
            'image' => 'required|mimes:jpeg,png,jpg,gif,svg,webp|max:5000',
        ]);
        try {
            $file = $request->file('image');
            $extension = $file->getClientOriginalExtension();
            $filename = Str::random(10) . '.' . $extension;
            $filePath = 'img/profesional/' . $filename;
            $file->move(public_path('img/profesional'), $filename);
            Professional::create([
                'name' => $request->name,
                'content' => $request->content,
                'image' => $filePath,
            ]);
            Alert::toast('Formación profesional creada exitosamente.', 'success');
            return redirect()->route('admin.pages.professional');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al crear la formación profesional. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //eliminar formación profesional
    public function deleteProfessional($id)
    {
        try {
            $professional = Professional::findOrFail($id);
            if ($professional->image && file_exists(public_path($professional->image))) {
                unlink(public_path($professional->image));
            }
            $professional->delete();
            Alert::toast('Formación profesional eliminada exitosamente.', 'success');
            return redirect()->route('admin.pages.professional');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al eliminar la formación profesional. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //actualizar formación profesional
    public function updateProfessional(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|mimes:jpeg,png,jpg,gif,svg,webp|max:5000',
        ]);
        try {
            $professional = Professional::findOrFail($id);
            $data = [
                'name' => $request->name,
                'content' => $request->content,
            ];
            if ($request->hasFile('image')) {
                if ($professional->image && file_exists(public_path($professional->image))) {
                    unlink(public_path($professional->image));
                }
                $file = $request->file('image');
                $extension = $file->getClientOriginalExtension();
                $filename = Str::random(10) . '.' . $extension;
                $filePath = 'img/profesional/' . $filename;
                $file->move(public_path('img/profesional'), $filename);
                $data['image'] = $filePath;
            }
            $professional->update($data);
            Alert::toast('Formación profesional actualizada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al actualizar la formación profesional. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }
}
