<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use App\Mail\NewPasswordTemporalMail;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\URL;
use App\Models\ParticipantProfile;
use Illuminate\Support\Facades\DB;
use App\Mail\EmailToRegisterUserMail;

use Illuminate\Routing\Controller as BaseController;

class AdminUserController extends BaseController
{
    /**
     * Display a listing of the resource.
     */
    //constructor
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:Super Admin|Administrador');
    }

    public function index()
    {
        $users = User::all();
        return view('admin.users.index', compact('users'));
    }

    //crear nuevo usuario
    public function createUser()
    {
        return view('admin.users.create-user');
    }

    public function editUser($id)
    {
        $user = User::findOrFail($id);
        return view('admin.users.edit-user', compact('user'));
    }

    public function updateUser(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255',
        ]);
        $user = User::findOrFail($id);
        $user->update($request->all());
        return redirect()->route('admin.users')->with('success', 'Usuario actualizado exitosamente.');
    }

    //enviar correo con una nueva contraseña
    public function sendEmailNewPasswordTemporal($id)
    {
        try {
            $user = User::findOrFail($id);
            //contraseña temporal
            $password = Str::random(10);
            $user->update([
                'password' => Hash::make($password),
            ]);
            Mail::to($user->email)->send(new NewPasswordTemporalMail($password));
            Alert::toast('Una nueva contraseña ha sido enviada al usuario. Correo enviado exitosamente.', 'success');
            return redirect()->route('admin.users')->with('success', 'Una nueva contraseña ha sido enviada al usuario. Correo enviado exitosamente.');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al enviar el correo. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //registrar usuarios
    public function registerUser(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'role' => 'required|string',
        ]);
        if ($request->role == 'Administrador') {
            try {
                DB::beginTransaction();
                //la contrasena incluye la palabra "Avance", la hora y minuto en que se ha creado y 3 caracteres random
                $password = "Avance".date('H:i:s').Str::random(3);
                $user = User::create([
                    'name' => $request->name,
                    'last_name' => $request->last_name,
                    'email' => $request->email,
                    'email_verified_at' => now(),
                    'password' => Hash::make($password),
                ]);
                $user->assignRole('Administrador');
                DB::commit();
            } catch (\Throwable $th) {
                DB::rollBack();
                Alert::toast('Ocurrió un error al crear el usuario. Error: ' . $th->getMessage(), 'error');
                return redirect()->back()->withInput();
            }
        }
        if ($request->role == 'Participante') {
            try {
                DB::beginTransaction();
                //la contrasena incluye la palabra "Avance", la hora y minuto en que se ha creado y 3 caracteres random
                $password = "Avance".date('His').Str::random(3);
                $user = User::create([
                    'name' => $request->name,
                    'last_name' => $request->last_name,
                    'email' => $request->email,
                    'email_verified_at' => now(),
                    'password' => Hash::make($password),
                ]);
                $user->assignRole('Participante');
                ParticipantProfile::create([
                    'user_id' => $user->id,
                ]);
                //enviar correo para 
                Mail::to($request->email)->send(new EmailToRegisterUserMail($password));
                //confirmar transaccion
                DB::commit();
            } catch (\Throwable $th) {
                DB::rollBack();
                Alert::toast('Ocurrió un error al crear el usuario. Error: ' . $th->getMessage(), 'error');
                return redirect()->back()->withInput();
            }
        }
        Alert::toast('Usuario creado exitosamente.', 'success');
        return redirect()->route('admin.users')->with('success', 'Usuario creado exitosamente.');
    }

    //bloquear usuario
    public function blockUser($id)
    {
        try {
            $user = User::findOrFail($id);
            // $user->syncRoles(['Usuario bloqueado']);
            $user->email_verified_at = null;
            $user->save();
            Alert::toast('Usuario bloqueado exitosamente.', 'success');
            return redirect()->route('admin.users')->with('success', 'Usuario bloqueado exitosamente.');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al bloquear el usuario. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //desbloquear usuario
    public function unblockUser($id)
    {
        try {
            $user = User::findOrFail($id);
            $user->email_verified_at = now();
            $user->save();
            // $user->syncRoles(['Super Admin']);
            Alert::toast('Usuario desbloqueado exitosamente.', 'success');
            return redirect()->route('admin.users')->with('success', 'Usuario desbloqueado exitosamente.');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al desbloquear el usuario. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //ver editar perfil del usuario logueado
    public function viewEditProfile()
    {
        return view('admin.users.edit-profile');
    }

    //actualizar contrasena del usuario logueado
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => 'required|string|min:8|confirmed',
        ]);
        $user = Auth::user();
        if (!Hash::check($request->current_password, $user->password)) {
            Alert::toast('La contraseña actual no coincide.', 'error');
            return redirect()->back()->withInput();
        }
        User::where('id', $user->id)->update([
            'password' => Hash::make($request->password),
        ]);
        Alert::toast('Contraseña actualizada exitosamente.', 'success');
        return redirect()->route('admin.users')->with('success', 'Contraseña actualizada exitosamente.');
    }
}
