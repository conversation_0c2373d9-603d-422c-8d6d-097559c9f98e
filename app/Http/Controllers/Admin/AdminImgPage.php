<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\WebSubSectionImage;
use RealRashid\SweetAlert\Facades\Alert;
use App\Models\WebSectionImage;
use Illuminate\Support\Str;

class AdminImgPage extends Controller
{

    //subir multiples imagenes
    public function uploadMultipleImagesCustomers(Request $request, $id)
    {
        $request->validate([
            'images' => 'required|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:5000',
        ]);
        try {
            foreach ($request->file('images') as $image) {
                $imageExtension = $image->getClientOriginalExtension();
                $imageFilename = Str::random(20) . '.' . $imageExtension;
                $image->move(public_path('img/customers'), $imageFilename);
                WebSectionImage::create([
                    'image' => 'img/customers/' . $imageFilename,
                    'alt' => $request->alt,
                    'section_id' => $id,
                ]);
            }
            Alert::toast('Imágenes subidas exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al subir las imágenes. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    public function uploadImage(Request $request)
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5000',
        ]);
        try {
            $file = $request->file('image');
            $extension = $file->getClientOriginalExtension();
            $filename = Str::random(20) . '.' . $extension;
            $file->move(public_path('img/pages'), $filename);
            WebSectionImage::create([
                'image' => 'img/pages/' . $filename,
                'alt' => $request->alt,
                'section_id' => $request->section_id,
            ]);
            Alert::toast('Imagen subida exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al subir la imagen. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }


    //eliminar imagen de la galería de una seccion
    public function deleteSectionImage($id)
    {
        try {
            $image = WebSectionImage::findOrFail($id);
            //eliminar de la carpeta/servidor
            if (file_exists(public_path($image->image))) {
                unlink(public_path($image->image));
            }
            //eliminar de la base de datos
            $image->delete();
            Alert::toast('Imagen eliminada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al eliminar la imagen. Error: ' . $th->getMessage(), 'error');
            return redirect()->back();
        }
    }

    //eliminar imagen de la galería de una subseccion
    public function deleteSubSectionImage($id)
    {
        try {
            $image = WebSubSectionImage::findOrFail($id);
            //eliminar de la carpeta/servidor
            if (file_exists(public_path($image->image))) {
                unlink(public_path($image->image));
            }
            //eliminar de la base de datos
            $image->delete();
            Alert::toast('Imagen eliminada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al eliminar la imagen. Error: ' . $th->getMessage(), 'error');
            return redirect()->back();
        }
    }


    //GALERIA
    public function uploadGalleryImage(Request $request, $id)
    {
        $request->validate([
            'images' => 'required|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:5000',
        ]);
        try {
            foreach ($request->file('images') as $image) {
                $imageExtension = $image->getClientOriginalExtension();
                $imageFilename = Str::random(20) . '.' . $imageExtension;
                $image->move(public_path('img/galeria'), $imageFilename);
                WebSectionImage::create([
                    'image' => 'img/galeria/' . $imageFilename,
                    'alt' => $request->alt,
                    'section_id' => $id,
                ]);
            }
            Alert::toast('Imágenes subidas exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al subir las imágenes. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }
}
