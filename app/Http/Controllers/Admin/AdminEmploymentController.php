<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Employment;
use App\Models\Applicant;
use App\Models\Status;
use Illuminate\Support\Facades\Storage;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use App\Models\ParticipantProfile;
use Illuminate\Http\Response;

class AdminEmploymentController extends Controller
{
    //all jobs
    public function index()
    {
        $jobs = Employment::all();
        return view('admin.jobs.index', compact('jobs'));
    }

    //to create new post
    public function createNewPostJob(){
        $statuses = Status::where('id', '<=', 3)->get();
        return view('admin.jobs.newjob', compact('statuses'));
    }

    //view post for admin
    public function editjob($id)
    {
        $job = Employment::findOrFail($id);
        $statuses = Status::all();
        return view('admin.jobs.editjob', compact('job', 'statuses'));
    }


    public function updateJob(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'short_description' => 'nullable|string|max:255',
            'description' => 'required|string',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5000',
            'status_id' => 'required|exists:statuses,id',
        ]);
        try {
            $job = Employment::findOrFail($id);
            $slugJobTitle = Str::slug($request->title);
            $data = [
                'title' => $request->title,
                'short_description' => $request->short_description,
                'slug' => $slugJobTitle,
                'subtitle' => $request->subtitle,
                'description' => $request->description,
                'status_id' => $request->status_id,
            ];
            if ($request->hasFile('cover_image')) {
                // Guardar nueva imagen
                $file = $request->file('cover_image');
                $extension = $file->getClientOriginalExtension();
                $filename = Str::random(20) . '.' . $extension;
                $imagePath = $file->storeAs('jobs', $filename, 'public');

                // Agregar ruta de imagen a datos para actualizar
                $data['cover_image'] = $imagePath;

                // Eliminar imagen anterior si existe
                if ($job->cover_image && Storage::disk('public')->exists($job->cover_image)) {
                    Storage::disk('public')->delete($job->cover_image);
                }
            }
            $job->update($data);
            Alert::toast('Publicación actualizada exitosamente.', 'success');
            return redirect()->route('admin.recruitment');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al actualizar la publicación. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //store new post (for admin)
    public function newJob(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'short_description' => 'nullable|string|max:255',
            'description' => 'required|string',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5000',
            'status_id' => 'required|exists:statuses,id',
        ]);

        try {
            $file = $request->file('cover_image');
            $extension = $file->getClientOriginalExtension();
            $filename = Str::random(20) . '.' . $extension;
            $imagePath = $file->storeAs('jobs', $filename, 'public');
            
            $slugJobTitle = Str::slug($request->title);
            Employment::create([
                'title' => $request->title,
                'subtitle' => $request->subtitle,
                'short_description' => $request->short_description,
                'slug' => $slugJobTitle,
                'description' => $request->description,
                'cover_image' => $imagePath,
                'status_id' => $request->status_id,
            ]);
            Alert::toast('Publicación creada exitosamente.', 'success');
            return redirect()->route('admin.recruitment');

        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al crear la publicación. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //for user who want to apply
    public function applyPosition(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255',
            'phone' => 'required|integer',
            'social_networks' => 'nullable|json',
            'description' => 'required|string',
            'cv_file' => 'required|file|mimes:pdf|max:5000',
        ]);
        try {
            $file = $request->file('cv_file');
            $extension = $file->getClientOriginalExtension();
            $filename = Str::random(20) . '.' . $extension;
            $cvPath = $file->storeAs('cvs', $filename, 'private');
            Applicant::create([
                'name' => $request->name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'social_networks' => $request->social_networks,
                'description' => $request->description,
                'cv_file' => $cvPath,
                'employment_id' => $id,
                'status_id' => 1,
            ]);
            Alert::toast('Aplicación enviada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al enviar la aplicación. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    public function viewApplicantJob($id)
    {
        $job = Employment::findOrFail($id);
        $applicants = Applicant::where('employment_id', $id)->get();
        return view('admin.jobs.viewapplicants', compact('job', 'applicants'));
    }

    //for user finals
    public function showDetailEmployments($slug)
    {
        $employment = Employment::where('slug', $slug)
                       ->where('status_id', 1)
                       ->firstOrFail();
        $employments = Employment::where('status_id', 1)->latest()->take(3)->get();
        return view('web.pages.employment.details', compact('employment', 'employments'));
    }

    //show all jobs available
    public function showAllEmployments()
    {
        $employments = Employment::where('status_id', 1)->get();
        return view('web.pages.employment.index', compact('employments'));
    }


    //aplicantes a un trabajo
    public function viewApplicantsJob($id)
    {
        $job = Employment::findOrFail($id);
        $applicants = Applicant::where('employment_id', $id)->get();
        return view('admin.jobs.applicants-jobs', compact('job', 'applicants'));
    }

    public function downloadCvpdf($id = null)
    {
        $user = Auth::user();
        $profile = ParticipantProfile::where('user_id', $user->id)->firstOrFail();

        if (!$profile->cv_file || !Storage::disk('private')->exists($profile->cv_file)) {
            Alert::toast('No se encontró tu CV en el sistema.', 'error');
            return redirect()->back();
        }
        // return Storage::disk('private')->download($profile->cv_file);
        $absolutePath = Storage::disk('private')->path($profile->cv_file);
        return response()->download($absolutePath);
    }

    //ver detalles de postulante
    public function viewDetailsApplicant($id)
    {
        // $applicant = Applicant::findOrFail($id);
        $applicant = Applicant::where('id', $id)->firstOrFail();
        $profile = ParticipantProfile::where('user_id', $applicant->user->id)->firstOrFail();
        $cvPath = Storage::disk('private')->path($profile->cv_file);
        $statuses = Status::skip(6)->take(4)->get();
        return view('admin.jobs.view-applicant', compact('applicant', 'cvPath', 'profile', 'statuses'));
    }

    //actualizar estado de postulante
    public function updateStatusApplicant(Request $request, $id)
    {
        $request->validate([
            'status_id' => 'required|exists:statuses,id',
        ]);
        try {
            $applicant = Applicant::findOrFail($id);
            $applicant->update([
                'status_id' => $request->status_id,
            ]);
            Alert::toast('Estado actualizado exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al actualizar el estado. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    public function getCvFile($id)
    {
        $profile = ParticipantProfile::where('user_id', $id)->firstOrFail();
        if (Storage::disk('private')->exists($profile->cv_file)) {
            $path = Storage::disk('private')->path($profile->cv_file);
            return new Response(file_get_contents($path), 200, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="' . basename($path) . '"',
            ]);
        }

        abort(404, 'Archivo no encontrado.');
    }
}
