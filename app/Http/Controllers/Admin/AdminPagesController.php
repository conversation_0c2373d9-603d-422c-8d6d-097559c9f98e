<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\WebPage;
use App\Models\WebSection;
use App\Models\WebSubSection;
use App\Models\WebTypeSection;
use App\Models\HeaderPage;
use App\Models\Status;
use App\Models\CorporateValue;
use App\Models\CompanyInformation;
use RealRashid\SweetAlert\Facades\Alert;
use App\Models\User;
use App\Models\Employment;

class AdminPagesController extends Controller
{
    public function index()
    {
        //USUARIOS
        $companyInformation = CompanyInformation::first();
        return view('admin.index', compact('companyInformation'));
    }

    public function updateCompanyInformation(Request $request)
    {
        $request->validate([
            'meta_keywords' => 'required|string',
            'meta_description' => 'required|string',
        ]);
        try {
            $companyInformation = CompanyInformation::first();
            $companyInformation->update($request->all());
            Alert::toast('Información actualizada exitosamente.', 'success');
            return redirect()->back()->with('success', 'Información actualizada exitosamente.');
        } catch (\Throwable $th) {
            return redirect()->back()->with('error', 'Ocurrió un error al actualizar la información. Error: ' . $th->getMessage());
        }
    }

    //mostrar todos los empleos disponibles
    public function showAllEmployments()
    {
        $empleos = WebPage::where('name', 'Empleos')->first();
        $header = HeaderPage::where('page_id', $empleos->id)->first();
        $employments = Employment::where('status_id', 1)->get();
        return view('web.pages.employment.index', compact('employments', 'header'));
    }

    public function HomePage()
    {
        return view('admin.pages.home');
    }

    public function AboutPage()
    {
        return view('admin.pages.aboutus');
    }

    public function ConsultingPage()
    {
        return view('admin.pages.consulting');
    }

    public function ProfessionalPage()
    {
        return view('admin.pages.professional');
    }

    public function ContactPage()
    {
        return view('admin.pages.contact');
    }


    public function NewsletterPage()
    {
        return view('admin.newsletter');
    }

    public function ContactMessagePage()
    {
        return view('admin.contactmessage');
    }

    public function UserPage()
    {
        return view('admin.user');
    }

    public function RecruitmentPage()
    {
        return view('admin.recruitment');
    }
}
