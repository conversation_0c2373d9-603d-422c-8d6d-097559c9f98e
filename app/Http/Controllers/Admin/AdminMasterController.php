<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\WebPage;
use App\Models\WebSection;
use App\Models\WebSectionImage;
use App\Models\WebSubSection;
use App\Models\WebSubSectionImage;
use App\Models\HeaderPage;
use Illuminate\Support\Str;
use RealRashid\SweetAlert\Facades\Alert;

class AdminMasterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //secciones
        $masterPage = WebPage::where('name', 'Master')->first();
        $secciones = WebSection::where('page_id', $masterPage->id)->get();
        //headers
        $headers = HeaderPage::all();
        //footer
        $footer = WebSection::where('name', 'Footer')->first();
        //boletin
        $boletin = WebSection::where('name', 'Boletín')->first();
        //contactanos
        $contactanos = WebSection::where('name', 'Contactanos')->first();
        //clientes
        $clientes = WebSection::where('name', 'Clientes')->first();
        return view('admin.pages.master.index', compact('headers', 'footer', 'boletin', 'contactanos', 'clientes', 'secciones'));
    }

    public function viewDetails($id)
    {
        $section = WebSection::with('subsections')->findOrFail($id);
        return view('admin.pages.master.sections', compact('section'));
    }


    //ver header
    public function headerView($id)
    {
        $header = HeaderPage::findOrFail($id);
        return view('admin.pages.master.header', compact('header'));
    }

    //actualizar header
    public function updateHeader(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'img_header' => 'image|mimes:jpeg,png,jpg,gif,webp|max:5000',
        ]);
        try {
            $header = HeaderPage::findOrFail($id);
            $data = [
                'title' => $request->title,
            ];
            if ($request->hasFile('img_header')) {
                if ($header->img_header && file_exists(public_path($header->img_header))) {
                    unlink(public_path($header->img_header));
                }
                $file = $request->file('img_header');
                $extension = $file->getClientOriginalExtension();
                $filename = Str::random(20) . '.' . $extension;
                $file->move(public_path('img/header'), $filename);
                $data['img_header'] = 'img/header/'.$filename;
            }
            $header->update($data);
            Alert::toast('Información actualizada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al actualizar el header. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //actualizar sección
    public function updateSection(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'sub_title' => 'required|string|max:255',
            'content' => 'required|string',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5000',
        ]);
        try {
            $section = WebSection::findOrFail($id);
            $data = [
                'title' => $request->title,
                'sub_title' => $request->sub_title,
                'content' => $request->content,
            ];
            if ($request->hasFile('cover_image')) {
                if ($section->cover_image && file_exists(public_path($section->cover_image))) {
                    unlink(public_path($section->cover_image));
                }
                $file = $request->file('cover_image');
                $extension = $file->getClientOriginalExtension();
                $filename = Str::random(20) . '.' . $extension;
                $file->move(public_path('img/master/'), $filename);
                $data['cover_image'] = 'img/master/'.$filename;
            }
            $section->update($data);
            Alert::toast('Información actualizada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al actualizar la sección. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }
}
