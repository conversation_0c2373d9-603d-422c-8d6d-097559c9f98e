<?php

namespace App\Http\Controllers\Pages;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\BlogPost;
use App\Models\Employment;
use App\Models\BannerSection;
use App\Models\WebPage;
use App\Models\WebSection;
use App\Models\CorporateValue;
use App\Models\WebSubSection;
use App\Models\HeaderPage;
use App\Models\NewsletterSubscriber;
use App\Models\Professional;
use App\Models\WebSectionImage;
use App\Models\WebSubSectionImage;
use App\Services\FormLoggerService;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Support\Facades\Cache;

class PagesController extends Controller
{
    //HOME
    public function index()
    {
        $inicioPage = WebPage::where('name', 'Inicio')->with(['sections' => function ($query) 
        {
            $query->where('name', 'Nosotros')
                ->orWhere('name', 'Servicios')
                ->with(['subsections' => function ($query) {
                    $query->where('name', 'Detalles de servicios');
                }]);
        }
        ])->first();
        $nosotros = $inicioPage->sections->firstWhere('name', 'Nosotros');
        $servicios = $inicioPage->sections->firstWhere('name', 'Servicios');
        $detalleServicios = $servicios->subsections;
        $banners = BannerSection::all()->sortBy('order');
        $valores = CorporateValue::all();
        $blogPosts = BlogPost::where('status_id', 1)->latest()->take(3)->get();
        $employments = Employment::where('status_id', 1)->latest()->take(3)->get();

        return view('web.pages.home.index', 
            compact('banners', 'nosotros', 'servicios', 'detalleServicios', 'valores', 'blogPosts', 'employments')
        );
    }

    // NOSOTROS
    public function nosotros()
    {
        $nosotrosPage = WebPage::where('name', 'Nosotros')->first();
        $nosotros = WebSection::where('name', 'Nuestra empresa')->first();
        $header = HeaderPage::where('page_id', $nosotrosPage->id)->first();
        //seccion de mision y vision
        $seccionVM = WebSection::where('name', 'Visión y Misión')->first();
        $mision = WebSubSection::where('name', 'Detalles de Misión')->first();
        $vision = WebSubSection::where('name', 'Detalles de Visión')->first();
        $galeria = WebSection::where('name', 'Galería')->first();
        $imagesGaleria = $galeria->section_images;
        //seccion de valores
        $valores = CorporateValue::all();
        return view('web.pages.about.index', compact('valores', 'header', 'seccionVM', 'nosotros', 'mision', 'vision', 'galeria', 'imagesGaleria'));
    }

    //contactenos web
    public function ContactSection()
    {
        $contactanos = WebPage::where('name', 'Contactanos')->first();
        $header = HeaderPage::where('page_id', $contactanos->id)->first();
        return view('web.pages.contact.index', compact('header'));
    }

    //consultorias web
    public function ConsultingSection()
    {
        $consultoriasPage = WebPage::where('name', 'Consultorias')->first();
        $header = HeaderPage::where('page_id', $consultoriasPage->id)->first();
        $consultorias = WebSection::where('name', 'Consultorías')->first();
        $detalleConsultorias = WebSubSection::where('section_id', $consultorias->id)->get();

        // Obtener los IDs de todas las consultorías para buscar sus imágenes
        $subSectionIds = $detalleConsultorias->pluck('id');

        // Ahora, busca las imágenes que corresponden a esos IDs de subsección
        $imagesConsultorias = WebSubSectionImage::whereIn('subsection_id', $subSectionIds)->get();

        $totalConsultorias = $detalleConsultorias->count();
        $consultoriaImpar = null;

        if ($totalConsultorias % 2 != 0) {
            // Si es impar, separamos la última consultoría
            $consultoriaImpar = $detalleConsultorias->last();
            $detalleConsultorias = $detalleConsultorias->slice(0, $totalConsultorias - 1);
        }

        $mitad = $detalleConsultorias->count() / 2;

        $primeraMitad = $detalleConsultorias->take($mitad);
        $segundaMitad = $detalleConsultorias->skip($mitad);

        return view('web.pages.consulting.index', compact('header', 'consultorias', 'imagesConsultorias', 'primeraMitad', 'segundaMitad', 'consultoriaImpar'));
    }

    //formacion profesional web
    public function ProfessionalSection()
    {
        $profesionales = Professional::all();
        return view('web.pages.professional.index', compact('profesionales'));
    }

    //seccion de servicios
    public function ServicesSection()
    {
        return view('admin.pages.services');
    }

    //EMPLEOS
    public function showDetailEmployments($slug)
    {
        $employment = Employment::where('slug', $slug)
                       ->where('status_id', 1)
                       ->firstOrFail();
        $employments = Employment::where('status_id', 1)->latest()->take(3)->get();
        return view('web.pages.employment.details', compact('employment', 'employments'));
    }

    public function showAllEmployments()
    {
        $empleos = WebPage::where('name', 'Empleos')->first();
        $header = HeaderPage::where('page_id', $empleos->id)->first();
        $employments = Employment::where('status_id', 1)->get();
        return view('web.pages.employment.index', compact('employments', 'header'));
    }

    // FIN EMPLEOS

    public function ContactFormSection()
    {
        return view('admin.pages.contactform');
    }

    //SUSCRIPCIONES
    public function newSubscriber(Request $request)
    {
        //Honeypot (campo trampa invisible)
        if (!empty($request->website)) { // Campo oculto en el formulario
            Log::warning('Bot detected on newsletter subscription', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'email' => $request->email,
            ]);
            return redirect()->back();
        }

        //Rate limiting (máximo 1 suscripción por minuto por IP)
        if (Cache::has('newsletter_sub_' . $request->ip())) {
            return back()->withErrors([
                'error' => 'Demasiadas solicitudes desde tu IP. Inténtalo de nuevo en unos minutos.'
            ]);
        }
        Cache::put('newsletter_sub_' . $request->ip(), true, now()->addMinutes(60));

        //Validar dominio MX del email
        $emailDomain = substr(strrchr($request->email, "@"), 1);
        if (!checkdnsrr($emailDomain, 'MX')) {
            return back()->withErrors([
                'email' => 'El dominio de tu correo no existe o no es válido.'
            ]);
        }

        //Log de intento de suscripción
        Log::channel('user_activity')->info('Newsletter subscription attempt', [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'email' => $request->email,
            'timestamp' => now()->toISOString(),
        ]);

        //Validación de campos
        $validator = Validator::make($request->all(), [
            'full_name' => ['required', 'string', 'max:255', 'regex:/^[\pL\s]+$/u'],
            'company'   => ['nullable', 'string', 'max:255'],
            'email'     => ['required', 'string', 'email', 'max:255'],
            'country'   => ['required', 'string', 'max:255'],
        ], [
            'full_name.required' => 'Por favor, introduce tu nombre completo.',
            'full_name.string' => 'El nombre completo debe ser texto.',
            'full_name.max' => 'El nombre completo no debe exceder los :max caracteres.',
            'email.required' => 'Por favor, introduce tu correo electrónico.',
            'email.string' => 'El correo electrónico debe ser texto.',
            'email.email' => 'El formato del correo electrónico no es válido.',
            'email.max' => 'El correo electrónico no debe exceder los :max caracteres.',
            'country.required' => 'Por favor, introduce tu país.',
            'country.string' => 'El país debe ser texto.',
            'country.max' => 'El país no debe exceder los :max caracteres.',
        ]);

        $formData = [
            'full_name' => $request->full_name,
            'company' => $request->company,
            'email' => $request->email,
            'country' => $request->country,
        ];

        //Validación fallida
        if ($validator->fails()) {
            FormLoggerService::logNewsletterSubscription($request, $formData, false, 'Validation Failed');
            Log::channel('user_activity')->warning('Newsletter subscription validation failed', [
                'errors' => $validator->errors()->all(),
                'ip' => $request->ip(),
                'timestamp' => now()->toISOString(),
            ]);
            Alert::error('Error de Validación', 'Por favor, revisa nuevamente el formulario y corrige los campos resaltados.');
            return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
        }

        try {
            $findEmail = NewsletterSubscriber::where('email', $request->email)->first();
            if ($findEmail) {
                FormLoggerService::logNewsletterSubscription($request, $formData, false, 'Email already subscribed');
                Log::channel('forms')->info('Duplicate newsletter subscription attempt', [
                    'email' => $request->email,
                    'ip' => $request->ip(),
                    'timestamp' => now()->toISOString(),
                ]);
                Alert::info('Ya estás suscrito a nuestro boletín.', 'Tu correo ya ha sido registrado anteriormente');
                return redirect()->back()->withInput();
            } else {
                NewsletterSubscriber::create($formData);
                FormLoggerService::logNewsletterSubscription($request, $formData, true);
            }

            Alert::toast('Suscripción exitosa. Pronto recibirás contenido valioso directamente en tu correo electrónico.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            FormLoggerService::logNewsletterSubscription($request, $formData, false, $th->getMessage());
            Log::channel('errors')->error('Newsletter subscription failed', [
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString(),
                'ip' => $request->ip(),
                'data' => $formData,
                'timestamp' => now()->toISOString(),
            ]);
            Alert::toast('Ocurrió un error al procesar tu suscripción. Inténtalo de nuevo más tarde.', 'error');
            return redirect()->back()->withInput();
        }
    }

    //PUBLICACIONES DE BLOG
    public function showAllPosts()
    {
        $blog = WebPage::where('name', 'Blog')->first();
        $header = HeaderPage::where('page_id', $blog->id)->first();
        //ordenar de ultimo
        $blogPosts = BlogPost::where('status_id', 1)->latest()->get();
        return view('web.pages.blog.allposts', compact('blogPosts', 'header'));
    }

    //DETALLES DE BLOG -- LECTORES
    public function showDetailBlog($slug)
    {
        $post = BlogPost::where('slug', $slug)->firstOrFail();
        $blogPosts = BlogPost::where('status_id', 1)->latest()->take(3)->get();
        return view('web.pages.blog.details', compact('post', 'blogPosts'));
    }


    //REGISTROS DE USUARIOS
    public function acceder(){
        return view('web.pages.auth.login');
    }

    //acceder
    public function loginview(){
        return view('web.pages.auth.login');
    }

    public function registro(){
        return view('web.pages.auth.emailverifyform');
    }

    public function registerForm(){
        return view('web.pages.auth.register');
    }
}
