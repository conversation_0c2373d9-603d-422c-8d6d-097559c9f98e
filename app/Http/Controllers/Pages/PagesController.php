<?php

namespace App\Http\Controllers\Pages;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\BlogPost;
use App\Models\Employment;
use App\Models\BannerSection;
use App\Models\WebPage;
use App\Models\WebSection;
use App\Models\CorporateValue;
use App\Models\WebSubSection;

class PagesController extends Controller
{
    /**
    * ESTE CONTROLADOR ES PARA VER LAS PAGINAS DE INICIO Y SUS SECCIONES.
    * POR SER LA PAGINA DE INICIO, NO SE MUESTRAN LAS SECCIONES COMPLETAS O MAS DETALLES
    * PARA VISUALIZAR LAS SECCIONES COMPLETAS SE DEBE IR A LAS PAGINAS ESPECIFICAS DE CADA SECCION
    * PARA EDITARLAS/CONTROLARLAS IR A SUS CONTROLADORES RESPECTIVOS.
    * BOTONES DE CADA SECCION QUE REDIRECCIONEN A LA PAGINA COMPLETA DE CADA SECCION PERO NO SE PUEDEN CAMBIAR.
    * NO HAY FUNCION PARA CREAR SECCIONES.
    */

    //HOME
    public function index()
    {
        $inicioPage = WebPage::where('name', 'Inicio')->first();
        //seccion de banners
        $banners = BannerSection::all();
        //seccion de nosotros
        $nosotros = WebSection::where([
            ['page_id', '=', $inicioPage->id],
            ['name', '=', 'Nosotros']
        ])->first();
        //seccion de servicios y detalles
        $servicios = WebSection::where([
            ['page_id', '=', $inicioPage->id],
            ['name', '=', 'Servicios']
        ])->first();
        $detalleServicios = WebSubSection::where([
            ['section_id', '=', $servicios->id],
            ['name', '=', 'Detalles de servicios']
        ])->get();
        //seccion de valores
        $valores = CorporateValue::all();
        //ultimas 3 publicaciones de blog
        $blogPosts = BlogPost::where('status_id', 1)->latest()->take(3)->get();
        //ultimas 3 publicaciones de vacantes
        $employments = Employment::where('status_id', 1)->latest()->take(3)->get();
        return view('web.pages.home.index', compact('blogPosts', 'employments', 'nosotros', 'servicios', 'detalleServicios', 'valores', 'banners'));
    }

    // NOSOTROS
    public function nosotros()
    {
        $nosotrosPage = WebPage::where('name', 'Nosotros')->first();
        //header de la pagina

        //seccion de mision y vision

        //seccion de valores
        $valores = CorporateValue::all();
        return view('web.pages.about.index', compact('valores'));
    }

    //secciones de la pagina de inicio
    public function BannerSection()
    {
        return view('admin.pages.carousel');
    }

    //seccion de nosotros
    public function AboutSection()
    {
        $nosotros = WebPage::where('name', 'Nosotros')->first();
        
    }

    //seccion de servicios
    public function ServicesSection()
    {
        return view('admin.pages.services');
    }

    //EMPLEOS
    

    //BLOG
    //redirigir y mostrar detalles de cada publicacion del blog
    public function showDetailBlog($slug)
    {
        $post = BlogPost::where('slug', $slug)->firstOrFail();
        $blogPosts = BlogPost::where('status_id', 1)->latest()->take(3)->get();
        return view('web.pages.blog.details', compact('post', 'blogPosts'));
    }

    //mostrar todas las publicaciones de blog
    public function showAllPosts()
    {
        $blogPosts = BlogPost::where('status_id', 1)->get();
        return view('web.pages.blog.allposts', compact('blogPosts'));
    }


    public function ContactFormSection()
    {
        return view('admin.pages.contactform');
    }
}
