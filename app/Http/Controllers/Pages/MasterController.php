<?php

namespace App\Http\Controllers\Pages;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\BlogPost;
use App\Models\Employment;
use App\Models\BannerSection;
use App\Models\WebPage;
use App\Models\WebSection;
use App\Models\CorporateValue;
use App\Models\WebSubSection;
use App\Models\OurCustomer;
use App\Models\OurCustomerImage;


class MasterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    private function master()
    {
        return WebPage::where('name', 'Master')->first();
    }

    public function suscripcion($masterPage)
    {
        return WebSection::where([
            ['page_id', '=', $masterPage->id],
            ['name', '=', 'Boletín']
        ])->first();
    }

    public function sistemas($masterPage)
    {
        return WebSection::where([
            ['page_id', '=', $masterPage->id],
            ['name', '=', 'Sistemas en línea']
        ])->first();
    }

    public function clientes()
    {
        $clientes = WebSection::first();
        $clientesImages = OurCustomerImage::all();
        return view('web.master.components.customers', compact('clientes', 'clientesImages'));
    }


    public function index()
    {
        $masterPage = $this->master();
        $sistemas = $this->sistemas($masterPage);
        $suscripcion = $this->suscripcion($masterPage);
        $clientes = OurCustomer::first();
        $clientesImages = OurCustomerImage::all();
        return view('web.master.master', compact('sistemas', 'suscripcion', 'clientes', 'clientesImages'));
    }
}
