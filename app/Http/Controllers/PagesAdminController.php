<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class PagesAdminController extends Controller
{

    public function index()
    {
        return view('admin.index');
    }

    public function HomePage()
    {
        return view('admin.pages.home');
    }

    public function AboutPage()
    {
        return view('admin.pages.aboutus');
    }

    public function ConsultingPage()
    {
        return view('admin.pages.consulting');
    }

    public function ProfessionalPage()
    {
        return view('admin.pages.professional');
    }

    public function ContactPage()
    {
        return view('admin.pages.contact');
    }


    public function ProfilePage()
    {
        return view('admin.profile');
    }

    public function NewsletterPage()
    {
        return view('admin.newsletter');
    }

    public function ContactMessagePage()
    {
        return view('admin.contactmessage');
    }

    public function UserPage()
    {
        return view('admin.user');
    }

    public function BlogPage()
    {
        return view('admin.blog.blog');
    }

    public function BlogNewPage()
    {
        return view('admin.blog.newpost');
    }

    public function RecruitmentPage()
    {
        return view('admin.recruitment');
    }
}
