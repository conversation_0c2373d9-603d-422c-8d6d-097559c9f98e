<?php

namespace App\Http\Controllers;

use App\Models\NewsletterSubscriber;
use App\Services\FormLoggerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;

class NewsletterSubscriberController extends Controller
{

    // public function newSubscriber(Request $request){
    //     // Log the attempt
    //     Log::channel('user_activity')->info('Newsletter subscription attempt', [
    //         'ip' => $request->ip(),
    //         'user_agent' => $request->userAgent(),
    //         'email' => $request->email,
    //         'timestamp' => now()->toISOString(),
    //     ]);

    //     $request->validate([
    //         'full_name' => 'required|string|max:255',
    //         'company'   => 'nullable|string|max:255',
    //         'email'     => 'required|string|email|max:255',
    //         'country'   => 'required|string|max:255',
    //     ]);

    //     $formData = [
    //         'full_name' => $request->full_name,
    //         'company' => $request->company,
    //         'email' => $request->email,
    //         'country' => $request->country,
    //     ];

    //     try {
    //         $findEmail = NewsletterSubscriber::where('email', $request->email)->first();
    //         if($findEmail){
    //             // Log duplicate subscription attempt
    //             FormLoggerService::logNewsletterSubscription($request, $formData, false, 'Email already subscribed');

    //             Log::channel('forms')->info('Duplicate newsletter subscription attempt', [
    //                 'email' => $request->email,
    //                 'ip' => $request->ip(),
    //                 'timestamp' => now()->toISOString(),
    //             ]);

    //             Alert::info('Ya estás suscrito a nuestro boletín.', 'Tu correo ya ha sido registrado anteriormente');
    //             return redirect()->back();
    //         } else {
    //             NewsletterSubscriber::create([
    //                 'full_name' => $request->full_name,
    //                 'company'   => $request->company,
    //                 'email'     => $request->email,
    //                 'country'   => $request->country,
    //             ]);

    //             // Log successful subscription
    //             FormLoggerService::logNewsletterSubscription($request, $formData, true);
    //         }

    //         Alert::toast('Suscripción exitosa. Pronto recibirás contenido valioso directamente en tu correo electrónico.', 'success');
    //         return redirect()->back();
    //     } catch (\Throwable $th) {
    //         // Log failed subscription
    //         FormLoggerService::logNewsletterSubscription($request, $formData, false, $th->getMessage());

    //         // Log error details
    //         Log::channel('errors')->error('Newsletter subscription failed', [
    //             'error' => $th->getMessage(),
    //             'trace' => $th->getTraceAsString(),
    //             'ip' => $request->ip(),
    //             'data' => $formData,
    //             'timestamp' => now()->toISOString(),
    //         ]);

    //         Alert::toast('Ocurrió un error al procesar tu suscripción. Inténtalo de nuevo más tarde.', 'error');
    //         return redirect()->back()->withInput();
    //     }
    // }
    // public function newSubscriber(Request $request)
    // {
    //     // Log the attempt
    //     Log::channel('user_activity')->info('Newsletter subscription attempt', [
    //         'ip' => $request->ip(),
    //         'user_agent' => $request->userAgent(),
    //         'email' => $request->email,
    //         'timestamp' => now()->toISOString(),
    //     ]);

    //     // Define the validation rules and custom messages
    //     $validator = Validator::make($request->all(), [
    //         'full_name' => ['required', 'string', 'max:255', 'regex:/^[\pL\s]+$/u'],
    //         'company'   => ['nullable', 'string', 'max:255'],
    //         'email'     => ['required', 'string', 'email', 'max:255'],
    //         'country'   => ['required', 'string', 'max:255'],
    //     ], [
    //         'full_name.required' => 'Por favor, introduce tu nombre completo.',
    //         'full_name.string' => 'El nombre completo debe ser texto.',
    //         'full_name.max' => 'El nombre completo no debe exceder los :max caracteres.',
    //         'email.required' => 'Por favor, introduce tu correo electrónico.',
    //         'email.string' => 'El correo electrónico debe ser texto.',
    //         'email.email' => 'El formato del correo electrónico no es válido.',
    //         'email.max' => 'El correo electrónico no debe exceder los :max caracteres.',
    //         'country.required' => 'Por favor, introduce tu país.',
    //         'country.string' => 'El país debe ser texto.',
    //         'country.max' => 'El país no debe exceder los :max caracteres.',
    //     ]);

    //     $formData = [
    //         'full_name' => $request->full_name,
    //         'company' => $request->company,
    //         'email' => $request->email,
    //         'country' => $request->country,
    //     ];

    //     // Check for validation failure
    //     if ($validator->fails()) {
    //         // Log validation failure
    //         FormLoggerService::logNewsletterSubscription($request, $formData, false, 'Validation Failed');
    //         Log::channel('user_activity')->warning('Newsletter subscription validation failed', [
    //             'errors' => $validator->errors()->all(),
    //             'ip' => $request->ip(),
    //             'timestamp' => now()->toISOString(),
    //         ]);

    //         Alert::error('Error de Validación', 'Por favor, revisa nuevamente el formulario y corrige los campos resaltados.');
    //         return redirect()->back()
    //                 ->withErrors($validator)
    //                 ->withInput();
    //     }

    //     try {
    //         $findEmail = NewsletterSubscriber::where('email', $request->email)->first();
    //         if ($findEmail) {
    //             // Log duplicate subscription attempt
    //             FormLoggerService::logNewsletterSubscription($request, $formData, false, 'Email already subscribed');

    //             Log::channel('forms')->info('Duplicate newsletter subscription attempt', [
    //                 'email' => $request->email,
    //                 'ip' => $request->ip(),
    //                 'timestamp' => now()->toISOString(),
    //             ]);
    //             Alert::info('Ya estás suscrito a nuestro boletín.', 'Tu correo ya ha sido registrado anteriormente');
    //             return redirect()->back()->withInput(); // Keep input even on duplicate to avoid retyping
    //         } else {
    //             NewsletterSubscriber::create([
    //                 'full_name' => $request->full_name,
    //                 'company'   => $request->company,
    //                 'email'     => $request->email,
    //                 'country'   => $request->country,
    //             ]);
    //             // Log successful subscription
    //             FormLoggerService::logNewsletterSubscription($request, $formData, true);
    //         }

    //         Alert::toast('Suscripción exitosa. Pronto recibirás contenido valioso directamente en tu correo electrónico.', 'success');
    //         return redirect()->back();
    //     } catch (\Throwable $th) {
    //         // Log failed subscription
    //         FormLoggerService::logNewsletterSubscription($request, $formData, false, $th->getMessage());
    //         // Log error details
    //         Log::channel('errors')->error('Newsletter subscription failed', [
    //             'error' => $th->getMessage(),
    //             'trace' => $th->getTraceAsString(),
    //             'ip' => $request->ip(),
    //             'data' => $formData,
    //             'timestamp' => now()->toISOString(),
    //         ]);
    //         Alert::toast('Ocurrió un error al procesar tu suscripción. Inténtalo de nuevo más tarde.', 'error');
    //         return redirect()->back()->withInput();
    //     }
    // }

    public function newSubscriber(Request $request)
    {
        //Honeypot (campo trampa invisible)
        if (!empty($request->website)) { // Campo oculto en el formulario
            Log::warning('Bot detected on newsletter subscription', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'email' => $request->email,
            ]);
            return redirect()->back();
        }

        //Rate limiting (máximo 1 suscripción por minuto por IP)
        if (Cache::has('newsletter_sub_' . $request->ip())) {
            return back()->withErrors([
                'error' => 'Demasiadas solicitudes desde tu IP. Inténtalo de nuevo en unos minutos.'
            ]);
        }
        Cache::put('newsletter_sub_' . $request->ip(), true, now()->addMinutes(1));

        //Validar dominio MX del email
        $emailDomain = substr(strrchr($request->email, "@"), 1);
        if (!checkdnsrr($emailDomain, 'MX')) {
            return back()->withErrors([
                'email' => 'El dominio de tu correo no existe o no es válido.'
            ]);
        }

        //Log de intento de suscripción
        Log::channel('user_activity')->info('Newsletter subscription attempt', [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'email' => $request->email,
            'timestamp' => now()->toISOString(),
        ]);

        //Validación de campos
        $validator = Validator::make($request->all(), [
            'full_name' => ['required', 'string', 'max:255', 'regex:/^[\pL\s]+$/u'],
            'company'   => ['nullable', 'string', 'max:255'],
            'email'     => ['required', 'string', 'email', 'max:255'],
            'country'   => ['required', 'string', 'max:255'],
        ], [
            'full_name.required' => 'Por favor, introduce tu nombre completo.',
            'full_name.string' => 'El nombre completo debe ser texto.',
            'full_name.max' => 'El nombre completo no debe exceder los :max caracteres.',
            'email.required' => 'Por favor, introduce tu correo electrónico.',
            'email.string' => 'El correo electrónico debe ser texto.',
            'email.email' => 'El formato del correo electrónico no es válido.',
            'email.max' => 'El correo electrónico no debe exceder los :max caracteres.',
            'country.required' => 'Por favor, introduce tu país.',
            'country.string' => 'El país debe ser texto.',
            'country.max' => 'El país no debe exceder los :max caracteres.',
        ]);

        $formData = [
            'full_name' => $request->full_name,
            'company' => $request->company,
            'email' => $request->email,
            'country' => $request->country,
        ];

        //Validación fallida
        if ($validator->fails()) {
            FormLoggerService::logNewsletterSubscription($request, $formData, false, 'Validation Failed');
            Log::channel('user_activity')->warning('Newsletter subscription validation failed', [
                'errors' => $validator->errors()->all(),
                'ip' => $request->ip(),
                'timestamp' => now()->toISOString(),
            ]);
            Alert::error('Error de Validación', 'Por favor, revisa nuevamente el formulario y corrige los campos resaltados.');
            return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
        }

        try {
            $findEmail = NewsletterSubscriber::where('email', $request->email)->first();
            if ($findEmail) {
                FormLoggerService::logNewsletterSubscription($request, $formData, false, 'Email already subscribed');
                Log::channel('forms')->info('Duplicate newsletter subscription attempt', [
                    'email' => $request->email,
                    'ip' => $request->ip(),
                    'timestamp' => now()->toISOString(),
                ]);
                Alert::info('Ya estás suscrito a nuestro boletín.', 'Tu correo ya ha sido registrado anteriormente');
                return redirect()->back()->withInput();
            } else {
                NewsletterSubscriber::create($formData);
                FormLoggerService::logNewsletterSubscription($request, $formData, true);
            }

            Alert::toast('Suscripción exitosa. Pronto recibirás contenido valioso directamente en tu correo electrónico.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            FormLoggerService::logNewsletterSubscription($request, $formData, false, $th->getMessage());
            Log::channel('errors')->error('Newsletter subscription failed', [
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString(),
                'ip' => $request->ip(),
                'data' => $formData,
                'timestamp' => now()->toISOString(),
            ]);
            Alert::toast('Ocurrió un error al procesar tu suscripción. Inténtalo de nuevo más tarde.', 'error');
            return redirect()->back()->withInput();
        }
    }

    public function showSubscribers(){
        $newsletterSubscribers = NewsletterSubscriber::all();
        return view('admin.newsletter', compact('newsletterSubscribers'));
    }

    public function editSubscriber($id)
    {
        $newsletterSubscriber = NewsletterSubscriber::find($id);
        return view('admin.newsletteredit', compact('newsletterSubscriber'));
    }
}
