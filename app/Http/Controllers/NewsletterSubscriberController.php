<?php

namespace App\Http\Controllers;

use App\Models\NewsletterSubscriber;
use App\Services\FormLoggerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use RealRashid\SweetAlert\Facades\Alert;

class NewsletterSubscriberController extends Controller
{

    public function newSubscriber(Request $request){
        // Log the attempt
        Log::channel('user_activity')->info('Newsletter subscription attempt', [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'email' => $request->email,
            'timestamp' => now()->toISOString(),
        ]);

        $request->validate([
            'full_name' => 'required|string|max:255',
            'company'   => 'nullable|string|max:255',
            'email'     => 'required|string|email|max:255',
            'country'   => 'required|string|max:255',
        ]);

        $formData = [
            'full_name' => $request->full_name,
            'company' => $request->company,
            'email' => $request->email,
            'country' => $request->country,
        ];

        try {
            $findEmail = NewsletterSubscriber::where('email', $request->email)->first();
            if($findEmail){
                // Log duplicate subscription attempt
                FormLoggerService::logNewsletterSubscription($request, $formData, false, 'Email already subscribed');

                Log::channel('forms')->info('Duplicate newsletter subscription attempt', [
                    'email' => $request->email,
                    'ip' => $request->ip(),
                    'timestamp' => now()->toISOString(),
                ]);

                Alert::info('Ya estás suscrito a nuestro boletín.', 'Tu correo ya ha sido registrado anteriormente');
                return redirect()->back();
            } else {
                NewsletterSubscriber::create([
                    'full_name' => $request->full_name,
                    'company'   => $request->company,
                    'email'     => $request->email,
                    'country'   => $request->country,
                ]);

                // Log successful subscription
                FormLoggerService::logNewsletterSubscription($request, $formData, true);
            }

            Alert::toast('Suscripción exitosa. Pronto recibirás contenido valioso directamente en tu correo electrónico.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            // Log failed subscription
            FormLoggerService::logNewsletterSubscription($request, $formData, false, $th->getMessage());

            // Log error details
            Log::channel('errors')->error('Newsletter subscription failed', [
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString(),
                'ip' => $request->ip(),
                'data' => $formData,
                'timestamp' => now()->toISOString(),
            ]);

            Alert::toast('Ocurrió un error al procesar tu suscripción. Inténtalo de nuevo más tarde.', 'error');
            return redirect()->back()->withInput();
        }
    }

    public function showSubscribers(){
        $newsletterSubscribers = NewsletterSubscriber::all();
        return view('admin.newsletter', compact('newsletterSubscribers'));
    }

    public function editSubscriber($id)
    {
        $newsletterSubscriber = NewsletterSubscriber::find($id);
        return view('admin.newsletteredit', compact('newsletterSubscriber'));
    }
}
