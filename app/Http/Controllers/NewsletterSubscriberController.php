<?php

namespace App\Http\Controllers;

use App\Models\NewsletterSubscriber;
use Illuminate\Http\Request;
use RealRashid\SweetAlert\Facades\Alert;
// Use Alert;
use Illuminate\Support\Facades\Toast;

class NewsletterSubscriberController extends Controller
{

    public function newSubscriber(Request $request){
        $request->validate([
            'full_name' => 'required|string|max:255',
            'company'   => 'nullable|string|max:255',
            'email'     => 'required|string|email|max:255',
            'country'   => 'required|string|max:255',
        ]);
        try {
            $findEmail = NewsletterSubscriber::where('email', $request->email)->first();
            if($findEmail){
                // Alert::toast('Ya estás suscrito a nuestro boletín.', 'info');
                Alert::info('Ya estás suscrito a nuestro boletín.', 'Tu correo ya ha sido registrado anteriormente');
                return redirect()->back();
            } else {
                NewsletterSubscriber::create([
                    'full_name' => $request->full_name,
                    'company'   => $request->company,
                    'email'     => $request->email,
                    'country'   => $request->country,
                ]);
            }
            Alert::toast('Suscripción exitosa. Pronto recibirás contenido valioso directamente en tu correo electrónico.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al procesar tu suscripción. Inténtalo de nuevo más tarde.', 'error');
            return redirect()->back()->withInput();
        }
    }

    public function showSubscribers(){
        $newsletterSubscribers = NewsletterSubscriber::all();
        return view('admin.newsletter', compact('newsletterSubscribers'));
    }

    public function editSubscriber($id)
    {
        $newsletterSubscriber = NewsletterSubscriber::find($id);
        return view('admin.newsletteredit', compact('newsletterSubscriber'));
    }
}
