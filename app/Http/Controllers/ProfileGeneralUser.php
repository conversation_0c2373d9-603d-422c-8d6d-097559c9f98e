<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\UserVerificationEmail;
use App\Models\User;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Support\Facades\Mail;
use App\Mail\TokenVerificationMail;

class ProfileGeneralUser extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('web.pages.auth.profile');
    }

    public function sendTokenVerificationEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
        ],[
            'email.required' => 'Por favor, ingresa tu correo electrónico.',
            'email.email'    => 'Por favor, ingresa un correo electrónico válido.',
            'email.unique'   => 'El correo electrónico ya está registrado.',
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        //veficar que el email no existe en la base de usuarios
        $existingUser = User::where('email', $request->email)->first();
        if ($existingUser) {
            Alert::toast('El email ya está registrado.', 'danger');
            return redirect()->route('iniciar-sesion');
        }
        do {
            $token = (string) random_int(10000000, 99999999);
        } while (UserVerificationEmail::where('token', $token)->exists());

        //guardar token en la base
        UserVerificationEmail::create([
            'email' => $request->email,
            'token' => $token,
            'expires_at' => now()->addMinutes(15),
        ]);
        //enviar email con el token
        Mail::to($request->email)->send(new TokenVerificationMail($token));
        Alert::toast('Recibirás un código de verificación para crear tu cuenta que deberás ingresar en el formulario de registro', 'success');
        return redirect()->route('formulario-registro');
    }
}
