<?php

namespace App\Http\Controllers;

use App\Models\CompanyInformation;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class CompanyInformationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): View
    {
        $companyInformation = CompanyInformation::paginate(10);

        return view('admin.company-information.index', compact('companyInformation'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('admin.company-information.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validator = Validator::make($request->all(), CompanyInformation::rules());

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $data = $request->validated();

            // Handle file uploads
            if ($request->hasFile('logo')) {
                $data['logo'] = $request->file('logo')->store('company/logos', 'public');
            }

            if ($request->hasFile('favicon')) {
                $data['favicon'] = $request->file('favicon')->store('company/favicons', 'public');
            }

            if ($request->hasFile('ico')) {
                $data['ico'] = $request->file('ico')->store('company/icons', 'public');
            }

            CompanyInformation::create($data);

            return redirect()->route('company-information.index')
                ->with('success', 'Información de la empresa creada exitosamente.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error al crear la información de la empresa: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(CompanyInformation $companyInformation): View
    {
        return view('admin.company-information.show', compact('companyInformation'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CompanyInformation $companyInformation): View
    {
        return view('admin.company-information.edit', compact('companyInformation'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CompanyInformation $companyInformation): RedirectResponse
    {
        $validator = Validator::make($request->all(), CompanyInformation::rules());

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $data = $request->validated();

            // Handle file uploads
            if ($request->hasFile('logo')) {
                // Delete old logo if exists
                if ($companyInformation->logo && Storage::disk('public')->exists($companyInformation->logo)) {
                    Storage::disk('public')->delete($companyInformation->logo);
                }
                $data['logo'] = $request->file('logo')->store('company/logos', 'public');
            }

            if ($request->hasFile('favicon')) {
                // Delete old favicon if exists
                if ($companyInformation->favicon && Storage::disk('public')->exists($companyInformation->favicon)) {
                    Storage::disk('public')->delete($companyInformation->favicon);
                }
                $data['favicon'] = $request->file('favicon')->store('company/favicons', 'public');
            }

            if ($request->hasFile('ico')) {
                // Delete old ico if exists
                if ($companyInformation->ico && Storage::disk('public')->exists($companyInformation->ico)) {
                    Storage::disk('public')->delete($companyInformation->ico);
                }
                $data['ico'] = $request->file('ico')->store('company/icons', 'public');
            }

            $companyInformation->update($data);

            return redirect()->route('company-information.index')
                ->with('success', 'Información de la empresa actualizada exitosamente.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error al actualizar la información de la empresa: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CompanyInformation $companyInformation): RedirectResponse
    {
        try {
            // Delete associated files
            if ($companyInformation->logo && Storage::disk('public')->exists($companyInformation->logo)) {
                Storage::disk('public')->delete($companyInformation->logo);
            }

            if ($companyInformation->favicon && Storage::disk('public')->exists($companyInformation->favicon)) {
                Storage::disk('public')->delete($companyInformation->favicon);
            }

            if ($companyInformation->ico && Storage::disk('public')->exists($companyInformation->ico)) {
                Storage::disk('public')->delete($companyInformation->ico);
            }

            $companyInformation->delete();

            return redirect()->route('company-information.index')
                ->with('success', 'Información de la empresa eliminada exitosamente.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error al eliminar la información de la empresa: ' . $e->getMessage());
        }
    }

    /**
     * Get company information for API.
     */
    public function getCompanyInfo(): JsonResponse
    {
        try {
            $companyInfo = CompanyInformation::first();

            if (!$companyInfo) {
                return response()->json([
                    'success' => false,
                    'message' => 'No se encontró información de la empresa.'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $companyInfo
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error al obtener la información de la empresa: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update company information via API.
     */
    public function updateCompanyInfo(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), CompanyInformation::rules());

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Datos de validación incorrectos.',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $companyInfo = CompanyInformation::first();

            if (!$companyInfo) {
                $companyInfo = CompanyInformation::create($request->validated());
            } else {
                $companyInfo->update($request->validated());
            }

            return response()->json([
                'success' => true,
                'message' => 'Información de la empresa actualizada exitosamente.',
                'data' => $companyInfo
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error al actualizar la información de la empresa: ' . $e->getMessage()
            ], 500);
        }
    }
}
