<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use App\Models\User;
use RealRashid\SweetAlert\Facades\Alert;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $request->email;
        $admins = User::where('email', $request->email)->first();
        if ($admins) {
            if ($admins->hasRole('Super Admin')) {
                $request->authenticate();
                $request->session()->regenerate();
                Alert::toast('Bienvenido al panel de administración.', 'success');
                return redirect()->intended(route('admin.home', absolute: false));
            }
            if ($admins->hasRole('Administrador')) {
                $request->authenticate();
                $request->session()->regenerate();
                Alert::toast('Bienvenido al panel de administración.', 'success');
                return redirect()->intended(route('admin.home', absolute: false));
            }
        }
        Alert::toast('Ocurrio un problema para validar tu cuenta. Por favor, contacta con el administrador.', 'error');
        return redirect()->back()->withErrors(['email' => 'Ocurrio un problema para validar tu cuenta. Por favor, contacta con el administrador.'])->withInput();
        // $request->authenticate();

        // $request->session()->regenerate();
        // if (Auth::user()->hasRole('Super Admin')) {
        //     return redirect()->intended(route('admin.home', absolute: false));
        // }
        // if (Auth::user()->hasRole('Administrador')) {
        //     return redirect()->intended(route('admin.home', absolute: false));
        // }
        // if (Auth::user()->hasRole('Usuario bloqueado')) {
        //     Auth::logout();
        //     return redirect()->route('login')->with('error', 'Tu cuenta ha sido bloqueada. Contacta con el administrador.');
        // }


        // return redirect()->intended(route('dashboard', absolute: false));
        // return redirect()->intended(route('admin.home', absolute: false));
        return redirect()->intended(route('dashboard', absolute: false));
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
