<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\BlogPost;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Support\Facades\Storage;
use App\Models\BlogCategory;
use App\Models\Status;
use Illuminate\Support\Facades\DB;

class BlogController extends Controller
{

    public function blogView()
    {
        $blogPosts = BlogPost::all();
        return view('admin.blog.blog', compact('blogPosts'));
    }

    public function createNewPostView()
    {
        $categories = BlogCategory::all();
        $statuses = Status::where('id', '<=', 3)->get();
        return view('admin.blog.newpost', compact('categories', 'statuses'));
    }

    public function blogPostView($id)
    {
        $blogPost = BlogPost::findOrFail($id);
        $categories = BlogCategory::all();
        $statuses = Status::where('id', '<=', 3)->get();
        return view('admin.blog.viewpost', compact('blogPost', 'categories', 'statuses'));
    }

    public function newPost(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'cover_image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5000',
            'published_at' => 'required|date',
            'category_id' => 'required|exists:blog_categories,id',
            'status_id' => 'required|exists:statuses,id',
        ]);

        try {
            DB::beginTransaction();
            // Procesar imagen
            $file = $request->file('cover_image');
            $extension = $file->getClientOriginalExtension();
            $filename = Str::random(20) . '.' . $extension;
            $imagePath = $file->storeAs('posts', $filename, 'public');
            //url del blog
            $slugBlogTitle = Str::slug($request->title);
            BlogPost::create([
                'title' => $request->title,
                'content' => $request->content,
                'image_path' => $imagePath,
                'slug' => $slugBlogTitle,
                'published_at' => $request->published_at,
                'category_id' => $request->category_id,
                'status_id' => $request->status_id,
                'user_id' => Auth::id(),
            ]);
            DB::commit();
            Alert::toast('Publicación creada exitosamente.', 'success');
            return redirect()->route('admin.blog');
        } catch (\Throwable $th) {
            DB::rollBack();
            // Eliminar imagen si se alcanzó a guardar
            if (!empty($imagePath) && Storage::disk('public')->exists($imagePath)) {
                Storage::disk('public')->delete($imagePath);
            }
            Alert::toast('Ocurrió un error al crear la publicación. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }
    


    public function update(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5000',
            'published_at' => 'required|date',
            'category_id' => 'required|exists:blog_categories,id',
            'status_id' => 'required|exists:statuses,id',
        ]);

        try {
            DB::beginTransaction();
            $slugBlogTitle = Str::slug($request->title);
            $post = BlogPost::findOrFail($id);

            $data = [
                'title' => $request->title,
                'content' => $request->content,
                'slug' => $slugBlogTitle,
                'published_at' => $request->published_at,
                'category_id' => $request->category_id,
                'status_id' => $request->status_id,
                'user_id' => Auth::id(),
            ];

            if ($request->hasFile('cover_image')) {
                // Guardar nueva imagen
                $file = $request->file('cover_image');
                $extension = $file->getClientOriginalExtension();
                $filename = Str::random(20) . '.' . $extension;
                $imagePath = $file->storeAs('posts', $filename, 'public');

                // Agregar ruta de imagen a datos para actualizar
                $data['image_path'] = $imagePath;

                // Eliminar imagen anterior si existe
                if ($post->image_path && Storage::disk('public')->exists($post->image_path)) {
                    Storage::disk('public')->delete($post->image_path);
                }
            }

            // Actualizar el post
            $post->update($data);
            DB::commit();
            Alert::toast('Publicación actualizada exitosamente.', 'success');
            return redirect()->route('admin.blog');

        } catch (\Throwable $th) {
            DB::rollBack();
            Alert::toast('Error al actualizar la publicación: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    public function showAllPosts()
    {
        $blogPosts = BlogPost::where('status_id', 1)->get();
        return view('web.pages.blog.allposts', compact('blogPosts'));
    }

    //for readers
    public function showDetailBlog($slug)
    {
        $post = BlogPost::where('slug', $slug)->firstOrFail();
        $blogPosts = BlogPost::where('status_id', 1)->latest()->take(3)->get();
        return view('web.pages.blog.details', compact('post', 'blogPosts'));
    }
}