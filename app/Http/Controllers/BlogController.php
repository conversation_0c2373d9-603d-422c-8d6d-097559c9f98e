<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\BlogPost;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Support\Facades\Storage;
use App\Models\BlogCategory;
use App\Models\Status;

class BlogController extends Controller
{

    public function blogView()
    {
        $blogPosts = BlogPost::all();
        return view('admin.blog.blog', compact('blogPosts'));
    }

    public function createNewPostView()
    {
        $categories = BlogCategory::all();
        $statuses = Status::where('id', '<=', 3)->get();
        return view('admin.blog.newpost', compact('categories', 'statuses'));
    }

    public function blogPostView($id)
    {
        $blogPost = BlogPost::find($id);
        return view('admin.blog.viewpost', compact('blogPost'));
    }

    public function storeTemp(Request $request)
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5000',
        ]);
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $fileName = 'temp_' . Str::random(20) . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('uploads/temp', $fileName, 'public');
            return response()->json(['path' => $path]);
        }

        return response()->json(['error' => 'No image uploaded.'], 400);
    }

    public function newPost(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'published_at' => 'required|date',
            'category_id' => 'required|exists:blog_categories,id',
            'status_id' => 'required|exists:statuses,id',
        ]);
        try {
            $imagePath = null;
            if ($request->filled('cover_image_path')) {
                $tempPath = $request->input('cover_image_path');
                if (Storage::disk('public')->exists($tempPath)) {
                    $newPath = 'uploads/posts/' . basename($tempPath);
                    Storage::disk('public')->move($tempPath, $newPath);
                    $imagePath = $newPath;
                }
            }
            BlogPost::create([
                'title' => $request->title,
                'content' => $request->content,
                'image_path' => $imagePath,
                'published_at' => $request->published_at,
                'category_id' => $request->category_id,
                'status_id' => $request->status_id,
                'user_id' => Auth::id(),
            ]);
            Alert::toast('Publicación creada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al crear la publicación. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }
}


// Ocurrió un error al crear la publicación. 
// Error: Add [title] to fillable property to allow mass assignment on [App\Models\BlogPost].