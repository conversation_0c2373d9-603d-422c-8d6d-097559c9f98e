<?php

namespace App\Http\Controllers;

use App\Models\ContactMessage;
use App\Services\FormLoggerService;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Log;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Validator;

class ContactMessageController extends Controller
{
    public function showMessages()
    {
        $contactMessages = ContactMessage::latest()->get();
        return view('admin.messages.contactmessage', compact('contactMessages'));
    }

    public function newContactMessage(Request $request)
    {
        Log::channel('user_activity')->info('Contact form submission attempt', [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now()->toISOString(),
        ]);
        $validator = Validator::make($request->all(), [
            'full_name' => ['required', 'string', 'max:255', 'regex:/^[\pL\s]+$/u'],
            'phone' => ['nullable', 'string', 'max:255', 'regex:/^[0-9]+$/'],
            'email' => ['required', 'email', 'max:255'],
            'subject' => ['required', 'string', 'max:255'],
            'message' => ['required', 'string'],
        ], [
            'full_name.required' => 'Por favor, introduce tu nombre completo.',
            'full_name.regex' => 'El nombre completo solo debe contener letras y espacios.',
            'phone.regex' => 'El teléfono solo debe contener números.',
            'email.required' => 'Por favor, introduce tu correo electrónico.',
            'email.email' => 'El formato del correo electrónico no es válido.',
            'subject.required' => 'Por favor, introduce el asunto.',
            'message.required' => 'Por favor, escribe tu mensaje.',
        ]);
        if ($validator->fails()) {
            $formData = [
                'full_name' => $request->full_name,
                'phone' => $request->phone,
                'email' => $request->email,
                'subject' => $request->subject,
                'message' => $request->message,
            ];
            FormLoggerService::logContactForm($request, $formData, false, 'Validation Failed');
            Log::channel('user_activity')->warning('Contact form validation failed', [
                'errors' => $validator->errors()->all(),
                'ip' => $request->ip(),
                'timestamp' => now()->toISOString(),
            ]);
            Alert::error('Error de Validación', 'Por favor, revisa nuevamente el formulario y corrige los campos resaltados.');
            return redirect()->back()
                             ->withErrors($validator)
                             ->withInput();
        }
        $formData = [
            'full_name' => $request->full_name,
            'phone' => $request->phone,
            'email' => $request->email,
            'subject' => $request->subject,
            'message' => $request->message,
        ];
        try {
            ContactMessage::create([
                'full_name' => $request->full_name,
                'phone'     => $request->phone,
                'email'     => $request->email,
                'subject'   => $request->subject,
                'message'   => $request->message,
                'ip_address' => $request->ip(),
                'read' => false,
            ]);
            FormLoggerService::logContactForm($request, $formData, true);
            Alert::toast('Mensaje enviado. Gracias por contactarnos. Nuestro equipo se pondrá en contacto contigo a la brevedad.', 'success');
            return redirect()->back();

        } catch (\Throwable $th) {
            // Log failed submission
            FormLoggerService::logContactForm($request, $formData, false, $th->getMessage());
            // Log error details
            Log::channel('errors')->error('Contact form submission failed', [
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString(),
                'ip' => $request->ip(),
                'data' => $formData,
                'timestamp' => now()->toISOString(),
            ]);
            Alert::toast('Ocurrió un error al procesar tu mensaje. Inténtalo de nuevo más tarde. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    public function viewMessage($id)
    {
        $viewMessage = ContactMessage::find($id);
        //marcar como leido editando en la base de datos el campo read a true
        $viewMessage->read = true;
        $viewMessage->save();
        return view('admin.messages.viewmessage', compact('viewMessage'));
    }

    //eliminar mensaje
    public function deleteMessage($id)
    {
        try {
            $message = ContactMessage::find($id);
            $message->delete();
            Alert::toast('Mensaje eliminado exitosamente.', 'success');
            return redirect()->route('admin.contactmessage');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al eliminar el mensaje. Error: ' . $th->getMessage(), 'error');
            return redirect()->back();
        }
    }
}