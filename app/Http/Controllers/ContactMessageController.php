<?php

namespace App\Http\Controllers;

use App\Models\ContactMessage;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use RealRashid\SweetAlert\Facades\Alert;

class ContactMessageController extends Controller
{
    public function showMessages()
    {
        $contactMessages = ContactMessage::all();
        return view('admin.contactmessage', compact('contactMessages'));
    }

    public function newContactMessage(Request $request){
        $request->validate([
            'full_name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);
        try {
            ContactMessage::create([
                'full_name' => $request->full_name,
                'phone'     => $request->phone,
                'email'     => $request->email,
                'subject'   => $request->subject,
                'message'   => $request->message,
                'ip_address' => $request->ip(),
                'read' => false,
            ]);
            Alert::toast('Mensaje enviado. Gracias por contactarnos. Nuestro equipo se pondrá en contacto contigo a la brevedad.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al procesar tu mensaje. Inténtalo de nuevo más tarde. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }
}
