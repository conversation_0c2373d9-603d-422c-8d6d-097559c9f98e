<?php

namespace App\Http\Controllers\GeneralUser;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\UserVerificationEmail;
use App\Models\User;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Support\Facades\Mail;
use App\Mail\TokenVerificationMail;
use Illuminate\Validation\Rules\Password;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Cache;

class AuthGeneralUser extends Controller
{
    // funciones para registrar candidatos
    public function sendTokenVerificationEmail(Request $request)
    {
        if (Cache::has('token_verification_' . $request->ip())) {
            Alert::toast('Inténtalo de nuevo en unos minutos.', 'danger');
            return back()->withErrors([
                'error' => 'Inténtalo de nuevo en unos minutos.'
            ]);
        }
        Cache::put('token_verification_' . $request->ip(), true, now()->addMinutes(1));

        $validator = Validator::make($request->all(), [
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
        ],[
            'email.required' => 'Por favor, ingresa tu correo electrónico.',
            'email.email'    => 'Por favor, ingresa un correo electrónico válido.',
            'email.unique'   => 'El correo electrónico ya está registrado. Por favor, inicia sesión.',
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        //veficar que el email no existe en la base de usuarios
        $existingUser = User::where('email', $request->email)->first();
        if ($existingUser) {
            Alert::toast('El email ya está registrado, por favor inicie sesión.', 'warning');
            return redirect()->route('iniciar-sesion');
        }
        do {
            $token = (string) random_int(10000000, 99999999);
        } while (UserVerificationEmail::where('token', $token)->exists());

        //guardar token en la base
        UserVerificationEmail::create([
            'email' => $request->email,
            'token' => $token,
            'expires_at' => now()->addMinutes(15),
        ]);
        //enviar email con el token
        Mail::to($request->email)->send(new TokenVerificationMail($token));
        Alert::toast('Recibirás un código de verificación para crear tu cuenta que deberás ingresar en el formulario de registro', 'success');
        return redirect()->route('formulario-registro');
    }

    public function registerUserGeneral(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'token'            => ['required', 'string', 'min:8', 'max:8'],
            'names'            => ['required', 'string', 'max:100'],
            'lastname'         => ['required', 'string', 'max:100'],
            'password'         => ['required', 'confirmed', Password::min(8)->mixedCase()->numbers()],
            'password_confirmation' => ['required'],
        ], [
            'token.required' => 'El código de verificación es obligatorio.',
            'token.min'      => 'El código de verificación debe tener 8 dígitos.',
            'token.max'      => 'El código de verificación debe tener 8 dígitos.',

            'names.required' => 'Por favor, ingresa tu nombre.',
            'names.string'   => 'El nombre debe ser texto.',
            'names.max'      => 'El nombre es demasiado largo.',

            'lastname.required' => 'Por favor, ingresa tu apellido.',
            'lastname.string'   => 'El apellido debe ser texto.',
            'lastname.max'      => 'El apellido es demasiado largo.',

            'password.required'   => 'Por favor, ingresa una contraseña.',
            'password.min'        => 'La contraseña debe tener al menos 8 caracteres.',
            'password.mixedCase'  => 'La contraseña debe incluir mayúsculas y minúsculas.',
            'password.numbers'    => 'La contraseña debe contener al menos un número.',
            'password.confirmed'  => 'Las contraseñas no coinciden.',
            'password_confirmation.required' => 'Por favor, confirma tu contraseña.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        $verifyToken = UserVerificationEmail::where('token', $request->token)
                ->where('expires_at', '>', now())
                ->first();
        if (!$verifyToken) {
            Alert::toast('Token inválido o expirado.', 'error');
            return redirect()->route('registro');
        }
        if ($verifyToken) {
            $email = $verifyToken->email;
            $user = User::create([
                'name' => $request->names,
                'last_name' => $request->lastname,
                'email' => $email,
                'email_verified_at' => now(),
                'password' => Hash::make($request->password),
            ]);
            $user->assignRole('Participante');
            $verifyToken->update([
                'verified_at' => now(),
            ]);
            Auth::login($user);
            Alert::toast('Registro exitoso.', 'success');
            return redirect()->route('home');
        } else {
            return redirect()->back()->withInput();
        }
        return redirect()->route('home');
    }

    public function login(Request $request)
    {
        // 1. Validar los datos de entrada
        $validator = Validator::make($request->all(), [
            'email'    => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
        ], [
            'email.required'    => 'El correo electrónico es obligatorio.',
            'email.email'       => 'El formato del correo electrónico es inválido.',
            'password.required' => 'La contraseña es obligatoria.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        //verificar que es un usuario general
        $user = User::where('email', $request->email)->first();
        if (!$user) {
            Alert::toast('El correo electrónico no está registrado.', 'error');
            return redirect()->back()->withErrors(['email' => 'El correo electrónico no está registrado.'])->withInput();
        }
        if (!$user->hasRole('Participante')) {
            Alert::toast('No tienes permisos para acceder a esta sección.', 'error');
            return redirect()->back()->withErrors(['email' => 'No tienes permisos para acceder a esta sección.'])->withInput();
        }
        try {
            $credentials = $request->only('email', 'password');
            if (Auth::attempt($credentials)) {
                $request->session()->regenerate();
                return redirect()->route('empleos');
            }
            throw ValidationException::withMessages([
                'email' => 'Las credenciales proporcionadas no coinciden con nuestros registros.',
            ])->redirectTo(redirect()->back()->withInput());
        } catch (\Throwable $th) {
            Alert::toast('Las credenciales proporcionadas no coinciden con nuestros registros.', 'error');
            return redirect()->back()->withErrors(['email' => 'Las credenciales proporcionadas no coinciden con nuestros registros.'])->withInput();
        }
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect()->route('home');
    }
}
