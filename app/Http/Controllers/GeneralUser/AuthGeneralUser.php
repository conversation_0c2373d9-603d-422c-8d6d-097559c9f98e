<?php

namespace App\Http\Controllers\GeneralUser;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\UserVerificationEmail;
use App\Models\User;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Support\Facades\Mail;
use App\Mail\TokenVerificationMail;
use Illuminate\Validation\Rules\Password;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Cache;
use App\Models\ParticipantProfile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\URL;
use App\Mail\TokenResetPasswordMail;

class AuthGeneralUser extends Controller
{
    // funciones para registrar candidatos
    public function sendTokenVerificationEmail(Request $request)
    {
        $expirationTime = now()->addMinutes(15);
        if (Cache::has('token_verification_' . $request->ip())) {
            Alert::toast('Inténtalo de nuevo en unos minutos.', 'danger');
            return back()->withErrors([
                'error' => 'Ya se solicitó un código. Por favor, revisa tu correo o espera 15 minutos.'
            ]);
        }
        // Almacena la marca de tiempo de expiración en la caché.
        Cache::put('token_verification_' . $request->ip(), $expirationTime->timestamp, $expirationTime);
        $validator = Validator::make($request->all(), [
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
        ],[
            'email.required' => 'Por favor, ingresa tu correo electrónico.',
            'email.email'    => 'Por favor, ingresa un correo electrónico válido.',
            'email.unique'   => 'El correo electrónico ya está registrado. Por favor, inicia sesión.',
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        //veficar que el email no existe en la base de usuarios
        $existingUser = User::where('email', $request->email)->first();
        if ($existingUser) {
            Alert::toast('El email ya está registrado, por favor inicie sesión.', 'warning');
            return redirect()->route('iniciar-sesion');
        }
        try {
            //transaccion
            DB::beginTransaction();
            //generar token
            do {
                $token = (string) random_int(10000000, 99999999);
            } while (UserVerificationEmail::where('token', $token)->exists());
            //guardar token en la base
            UserVerificationEmail::create([
                'email' => $request->email,
                'token' => $token,
                'expires_at' => now()->addMinutes(15),
            ]);
            //enviar email con el token
            Mail::to($request->email)->send(new TokenVerificationMail($token));
            //confirmar transaccion
            DB::commit();
            Alert::toast('Recibirás un código de verificación para crear tu cuenta que deberás ingresar en el formulario de registro', 'success');
            return redirect()->route('formulario-registro')->with('expirationTime', $expirationTime->timestamp);
        } catch (\Throwable $th) {
            DB::rollBack();
            Alert::toast('Ocurrió un error al enviar el correo de verificación. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    public function resendTokenVerificationEmail(Request $request)
    {
        $expirationTime = now()->addMinutes(15);
        // obtenemos el último email al que se le envió token en esta sesión
        $lastToken = UserVerificationEmail::latest()->first();
        if (!$lastToken) {
            Alert::toast('No se encontró un email asociado a la verificación.', 'danger');
            return redirect()->back();
        }
        $email = $lastToken->email;
        if (Cache::has('token_verification_' . $request->ip())) {
            Alert::toast('Inténtalo de nuevo en unos minutos.', 'danger');
            return back()->withErrors([
                'error' => 'Ya se solicitó un código. Por favor, revisa tu correo o espera 15 minutos.'
            ]);
        }
        Cache::put('token_verification_' . $request->ip(), $expirationTime->timestamp, $expirationTime);
        try {
            DB::beginTransaction();
            // generar token nuevo
            do {
                $token = (string) random_int(10000000, 99999999);
            } while (UserVerificationEmail::where('token', $token)->exists());
            // guardar nuevo token
            UserVerificationEmail::create([
                'email' => $email,
                'token' => $token,
                'expires_at' => now()->addMinutes(15),
            ]);
            //borrar token anterior
            UserVerificationEmail::where('email', $email)->where('id', '!=', $lastToken->id)->delete();
            // enviar email
            Mail::to($email)->send(new TokenVerificationMail($token));
            DB::commit();
            Alert::toast('Se envió un nuevo código de verificación a tu correo.', 'success');
            return redirect()->route('formulario-registro')->with('expirationTime', $expirationTime->timestamp);
        } catch (\Throwable $th) {
            DB::rollBack();
            Alert::toast('Ocurrió un error al reenviar el código. Error: ' . $th->getMessage(), 'error');
            return redirect()->back();
        }
    }


    public function registerUserGeneral(Request $request)
    {
        //limit rate
        if (Cache::has('register_user_' . $request->ip())) {
            Alert::toast('Inténtalo de nuevo en unos minutos.', 'danger');
            return back()->withErrors([
                'error' => 'Inténtalo de nuevo en unos minutos.'
            ]);
        }
        Cache::put('register_user_' . $request->ip(), true, now()->addMinutes(1));
        $validator = Validator::make($request->all(), [
            'token'            => ['required', 'string', 'min:8', 'max:8'],
            'names'            => ['required', 'string', 'max:100'],
            'lastname'         => ['required', 'string', 'max:100'],
            'password'         => ['required', 'confirmed', Password::min(8)->mixedCase()->numbers()],
            'password_confirmation' => ['required'],
        ], [
            'token.required' => 'El código de verificación es obligatorio.',
            'token.min'      => 'El código de verificación debe tener 8 dígitos.',
            'token.max'      => 'El código de verificación debe tener 8 dígitos.',

            'names.required' => 'Por favor, ingresa tu nombre.',
            'names.string'   => 'El nombre debe ser texto.',
            'names.max'      => 'El nombre es demasiado largo.',

            'lastname.required' => 'Por favor, ingresa tu apellido.',
            'lastname.string'   => 'El apellido debe ser texto.',
            'lastname.max'      => 'El apellido es demasiado largo.',

            'password.required'   => 'Por favor, ingresa una contraseña.',
            'password.min'        => 'La contraseña debe tener al menos 8 caracteres.',
            'password.mixedCase'  => 'La contraseña debe incluir mayúsculas y minúsculas.',
            'password.numbers'    => 'La contraseña debe contener al menos un número.',
            'password.confirmed'  => 'Las contraseñas no coinciden.',
            'password_confirmation.required' => 'Por favor, confirma tu contraseña.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        $verifyToken = UserVerificationEmail::where('token', $request->token)
                ->where('expires_at', '>', now())
                ->first();
        if (!$verifyToken) {
            Alert::toast('Token inválido o expirado.', 'error');
            return redirect()->route('registro');
        }
        if ($verifyToken) {
            //iniciar transaccion
            try {
                DB::beginTransaction();
                $email = $verifyToken->email;
                $user = User::create([
                    'name' => $request->names,
                    'last_name' => $request->lastname,
                    'email' => $email,
                    'email_verified_at' => now(),
                    'password' => Hash::make($request->password),
                ]);
                $user->assignRole('Participante');
                $verifyToken->update([
                    'verified_at' => now(),
                ]);
                ParticipantProfile::create([
                    'user_id' => $user->id,
                ]);
                Auth::login($user);
                //borrar token
                UserVerificationEmail::where('email', $email)->delete();
                //limpiar cache
                Cache::forget('token_verification_' . $request->ip());
                Cache::forget('register_user_' . $request->ip());
                //confirmar transaccion
                DB::commit();
                Alert::toast('Registro exitoso.', 'success');
                return redirect()->route('home');
            } catch (\Throwable $th) {
                DB::rollBack();
                Alert::toast('Ocurrió un error al crear tu cuenta. Error: ' . $th->getMessage(), 'error');
                return redirect()->back()->withInput();
            }
        } else {
            return redirect()->back()->withInput();
        }
    }

    public function login(Request $request)
    {
        // 1. Validar los datos de entrada
        $validator = Validator::make($request->all(), [
            'email'    => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
        ], [
            'email.required'    => 'El correo electrónico es obligatorio.',
            'email.email'       => 'El formato del correo electrónico es inválido.',
            'password.required' => 'La contraseña es obligatoria.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        //verificar que es un usuario general
        $user = User::where('email', $request->email)->first();
        if (!$user) {
            Alert::toast('El correo electrónico no está registrado.', 'error');
            return redirect()->back()->withErrors(['email' => 'El correo electrónico no está registrado.'])->withInput();
        }
        if($user->email_verified_at == null){
            Alert::toast('Ocurrio un problema para validar tu cuenta. Por favor, contacta con el administrador.', 'error');
            return redirect()->back()->withErrors(['email' => 'Ocurrio un problema para validar tu cuenta. Por favor, contacta con el administrador.'])->withInput();
        }
        if($user->hasRole('Usuario bloqueado')){
            Alert::toast('Tu cuenta ha sido bloqueada. Contacta con el administrador.', 'error');
            return redirect()->back()->withErrors(['email' => 'Tu cuenta ha sido bloqueada. Contacta con el administrador.'])->withInput();
        }
        if (!$user->hasRole('Participante')) {
            Alert::toast('No tienes permisos para acceder a esta sección.', 'error');
            return redirect()->back()->withErrors(['email' => 'No tienes permisos para acceder a esta sección.'])->withInput();
        }
        try {
            $credentials = $request->only('email', 'password');
            if (Auth::attempt($credentials)) {
                $request->session()->regenerate();
                Alert::toast('Bienvenido ' . Auth::user()->name, 'success');
                return redirect()->route('home');
            }
            throw ValidationException::withMessages([
                'email' => 'Las credenciales proporcionadas no coinciden con nuestros registros.',
            ])->redirectTo(redirect()->back()->withInput());
        } catch (\Throwable $th) {
            Alert::toast('Las credenciales proporcionadas no coinciden con nuestros registros.', 'error');
            return redirect()->back()->withErrors(['email' => 'Las credenciales proporcionadas no coinciden con nuestros registros.'])->withInput();
        }
    }

    public function logout(Request $request)
    {
        Auth::logout();
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        Alert::toast('Has cerrado sesión.', 'success');
        return redirect()->route('home');
    }



    //olvide contrasena || ingresar correo
    public function forgotPassword()
    {
        return view('web.pages.auth.forgot-password');
    }

    //vista para completar nueva contrasena
    public function resetPassword()
    {
        return view('web.pages.auth.reset-password');
    }

    //enviar token para recuperar contrasena:
    public function sendTokenResetPassword(Request $request)
    {
        //email 
        $request->validate([
            'email' => 'required|email',
        ]);
        //rate limiting
        if (Cache::has('token_forget_password_' . $request->ip())) {
            Alert::toast('Inténtalo de nuevo en unos minutos.', 'danger');
            return back()->withErrors([
                'error' => 'Inténtalo de nuevo en unos minutos.'
            ]);
        }
        Cache::put('token_forget_password_' . $request->ip(), true, now()->addMinutes(1));
        $user = User::where('email', $request->email)->first();
        if (!$user) {
            Alert::toast('El correo electrónico no existe.', 'error');
            return redirect()->back()->withInput();
        }
        //usuario de tipo Participante
        if (!$user->hasRole('Participante')) {
            Alert::toast('No tienes permisos para acceder a esta sección.', 'error');
            return redirect()->back()->withInput();
        }
        try {
            //generar token
            do {
                $token = (string) random_int(10000000, 99999999);
            } while (UserVerificationEmail::where('token', $token)->exists());
            //guardar token en la base
            UserVerificationEmail::create([
                'email' => $request->email,
                'token' => $token,
                'expires_at' => now()->addMinutes(15),
            ]);
            //enviar email con el token
            Mail::to($request->email)->send(new TokenResetPasswordMail($token));
            Alert::toast('Recibirás un código de verificación para recuperar tu contraseña.', 'success');
            return redirect()->route('reset-password-general-user');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al enviar el correo de verificación. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //validar token para cambiar/recuperar contrasena
    public function verifyTokenResetPassword(Request $request)
    {
        //request token y nueva contrasena y confirmacion
        $request->validate([
            'token' => 'required|string|min:8|max:8',
            'password' => 'required|string|min:8|confirmed',
        ]);
        $validator = Validator::make($request->all(), [
            'token' => 'required|string|min:8|max:8',
            'password' => 'required|string|min:8|confirmed',
        ],[
            'token.required' => 'El token es obligatorio.',
            'token.min' => 'El token debe tener 8 dígitos.',
            'token.max' => 'El token debe tener 8 dígitos.',
            'password.required' => 'La contraseña es obligatoria.',
            'password.min' => 'La contraseña debe tener al menos 8 caracteres.',
            'password.confirmed' => 'La confirmación de la contraseña no coincide.',
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $verifyToken = UserVerificationEmail::where('token', $request->token)
                ->where('expires_at', '>', now())
                ->first();
        if (!$verifyToken) {
            Alert::toast('Token inválido o expirado.', 'error');
            return redirect()->route('registro');
        }
        try {
            DB::beginTransaction();
            $user = User::where('email', $verifyToken->email)->first();
            $user->update([
                'password' => Hash::make($request->password),
            ]);
            $verifyToken->update([
                'verified_at' => now(),
            ]);
            //borrar token
            UserVerificationEmail::where('email', $verifyToken->email)->delete();
            DB::commit();
            Alert::toast('Contraseña actualizada exitosamente.', 'success');
            return redirect()->route('acceder');
        } catch (\Throwable $th) {
            DB::rollBack();
            Alert::toast('Ocurrió un error al actualizar la contraseña. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }
}
