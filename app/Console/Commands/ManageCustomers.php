<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\OurCustomer;
use App\Models\OurCustomerImage;

class ManageCustomers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'customers:manage 
                            {action : Action to perform (list, add, activate, deactivate, reorder)}
                            {--id= : Customer image ID for specific actions}
                            {--name= : Customer name}
                            {--image= : Image path}
                            {--alt= : Alt text}
                            {--url= : Website URL}
                            {--order= : Sort order}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage customer images from command line';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'list':
                $this->listCustomers();
                break;
            case 'add':
                $this->addCustomer();
                break;
            case 'activate':
                $this->toggleCustomer(true);
                break;
            case 'deactivate':
                $this->toggleCustomer(false);
                break;
            case 'reorder':
                $this->reorderCustomers();
                break;
            default:
                $this->error("Acción no válida. Usa: list, add, activate, deactivate, reorder");
        }
    }

    /**
     * List all customers
     */
    private function listCustomers()
    {
        $customers = OurCustomerImage::with('customer')
                                    ->orderBy('sort_order')
                                    ->get();

        if ($customers->isEmpty()) {
            $this->info('No hay clientes registrados.');
            return;
        }

        $this->info('📋 Lista de Clientes:');
        $this->line('');

        $headers = ['ID', 'Nombre', 'Imagen', 'Estado', 'Orden', 'URL'];
        $rows = [];

        foreach ($customers as $customer) {
            $rows[] = [
                $customer->id,
                $customer->name ?? 'Sin nombre',
                substr($customer->image, 0, 40) . '...',
                $customer->is_active ? '✅ Activo' : '❌ Inactivo',
                $customer->sort_order,
                $customer->website_url ? '🔗 Sí' : '❌ No'
            ];
        }

        $this->table($headers, $rows);
        $this->line('');
        $this->info("Total: {$customers->count()} clientes");
    }

    /**
     * Add a new customer
     */
    private function addCustomer()
    {
        $name = $this->option('name') ?? $this->ask('Nombre del cliente');
        $image = $this->option('image') ?? $this->ask('Ruta de la imagen');
        $alt = $this->option('alt') ?? $this->ask('Texto alternativo', $name);
        $url = $this->option('url') ?? $this->ask('URL del sitio web (opcional)');
        $order = $this->option('order') ?? $this->ask('Orden de visualización', OurCustomerImage::max('sort_order') + 1);

        // Obtener el customer principal
        $customer = OurCustomer::active()->first();
        
        if (!$customer) {
            $this->error('No hay un registro de cliente principal. Ejecuta el seeder primero.');
            return;
        }

        try {
            $customerImage = OurCustomerImage::create([
                'customer_id' => $customer->id,
                'name' => $name,
                'image' => $image,
                'alt_text' => $alt,
                'website_url' => $url ?: null,
                'is_active' => true,
                'sort_order' => (int) $order,
            ]);

            $this->info("✅ Cliente '{$name}' agregado exitosamente con ID: {$customerImage->id}");
        } catch (\Exception $e) {
            $this->error("❌ Error al agregar cliente: {$e->getMessage()}");
        }
    }

    /**
     * Toggle customer active status
     */
    private function toggleCustomer(bool $status)
    {
        $id = $this->option('id') ?? $this->ask('ID del cliente');
        
        $customer = OurCustomerImage::find($id);
        
        if (!$customer) {
            $this->error("Cliente con ID {$id} no encontrado.");
            return;
        }

        $customer->update(['is_active' => $status]);
        
        $statusText = $status ? 'activado' : 'desactivado';
        $this->info("✅ Cliente '{$customer->name}' {$statusText} exitosamente.");
    }

    /**
     * Reorder customers
     */
    private function reorderCustomers()
    {
        $this->info('🔄 Reordenando clientes automáticamente...');
        
        $customers = OurCustomerImage::orderBy('sort_order')->get();
        
        foreach ($customers as $index => $customer) {
            $customer->update(['sort_order' => ($index + 1) * 10]);
        }
        
        $this->info("✅ {$customers->count()} clientes reordenados exitosamente.");
    }
}
