<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;

class LogStatistics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logs:stats 
                            {--days=7 : Number of days to analyze}
                            {--type=all : Type of logs to analyze (all, forms, security, errors, user_activity)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Display logging statistics for the application';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $type = $this->option('type');

        $this->info("📊 Estadísticas de Logs - Últimos {$days} días");
        $this->line('');

        $logTypes = $type === 'all' ? ['forms', 'security', 'errors', 'user_activity'] : [$type];

        foreach ($logTypes as $logType) {
            $this->analyzeLogType($logType, $days);
        }

        $this->line('');
        $this->info('✅ Análisis completado');
    }

    /**
     * Analyze a specific log type.
     */
    private function analyzeLogType(string $logType, int $days): void
    {
        $logPath = storage_path("logs/{$logType}.log");
        
        if (!File::exists($logPath)) {
            $this->warn("⚠️  Log file not found: {$logType}.log");
            return;
        }

        $this->line("📋 Analizando: {$logType}.log");
        $this->line('─────────────────────────────');

        $stats = $this->getLogStatistics($logPath, $days);

        $this->table(
            ['Métrica', 'Valor'],
            [
                ['Total de entradas', number_format($stats['total'])],
                ['Entradas por día (promedio)', number_format($stats['daily_average'], 1)],
                ['Errores', number_format($stats['errors'])],
                ['Advertencias', number_format($stats['warnings'])],
                ['Info', number_format($stats['info'])],
                ['IPs únicas', number_format($stats['unique_ips'])],
                ['Tamaño del archivo', $this->formatBytes(File::size($logPath))],
            ]
        );

        if ($logType === 'forms') {
            $this->showFormStatistics($logPath, $days);
        } elseif ($logType === 'security') {
            $this->showSecurityStatistics($logPath, $days);
        }

        $this->line('');
    }

    /**
     * Get general statistics from a log file.
     */
    private function getLogStatistics(string $logPath, int $days): array
    {
        $content = File::get($logPath);
        $lines = explode("\n", $content);
        
        $cutoffDate = Carbon::now()->subDays($days);
        
        $stats = [
            'total' => 0,
            'errors' => 0,
            'warnings' => 0,
            'info' => 0,
            'unique_ips' => 0,
            'daily_average' => 0,
        ];

        $ips = [];
        $validLines = 0;

        foreach ($lines as $line) {
            if (empty(trim($line))) continue;

            // Check if line is within date range
            if (!$this->isLineInDateRange($line, $cutoffDate)) {
                continue;
            }

            $validLines++;

            // Count by log level
            if (strpos($line, '.ERROR:') !== false) {
                $stats['errors']++;
            } elseif (strpos($line, '.WARNING:') !== false) {
                $stats['warnings']++;
            } elseif (strpos($line, '.INFO:') !== false) {
                $stats['info']++;
            }

            // Extract IP addresses
            if (preg_match('/"ip_address":"([^"]+)"/', $line, $matches)) {
                $ips[$matches[1]] = true;
            }
        }

        $stats['total'] = $validLines;
        $stats['unique_ips'] = count($ips);
        $stats['daily_average'] = $days > 0 ? $validLines / $days : 0;

        return $stats;
    }

    /**
     * Show form-specific statistics.
     */
    private function showFormStatistics(string $logPath, int $days): void
    {
        $content = File::get($logPath);
        
        $contactForms = substr_count($content, 'contact_message');
        $newsletterSubs = substr_count($content, 'newsletter_subscription');
        $successful = substr_count($content, 'successfully');
        $failed = substr_count($content, 'failed');

        $this->line('📝 Estadísticas de Formularios:');
        $this->table(
            ['Tipo', 'Cantidad'],
            [
                ['Mensajes de contacto', $contactForms],
                ['Suscripciones newsletter', $newsletterSubs],
                ['Envíos exitosos', $successful],
                ['Envíos fallidos', $failed],
                ['Tasa de éxito', $successful + $failed > 0 ? round(($successful / ($successful + $failed)) * 100, 1) . '%' : 'N/A'],
            ]
        );
    }

    /**
     * Show security-specific statistics.
     */
    private function showSecurityStatistics(string $logPath, int $days): void
    {
        $content = File::get($logPath);
        
        $suspiciousActivity = substr_count($content, 'Suspicious activity');
        $suspiciousForms = substr_count($content, 'Suspicious') - $suspiciousActivity;
        $suspicious404 = substr_count($content, 'Suspicious 404');
        $methodNotAllowed = substr_count($content, 'Method not allowed');

        $this->line('🔒 Estadísticas de Seguridad:');
        $this->table(
            ['Tipo de Evento', 'Cantidad'],
            [
                ['Actividad sospechosa', $suspiciousActivity],
                ['Formularios sospechosos', $suspiciousForms],
                ['404 sospechosos', $suspicious404],
                ['Métodos no permitidos', $methodNotAllowed],
            ]
        );

        // Show top suspicious IPs
        $this->showTopSuspiciousIPs($content);
    }

    /**
     * Show top suspicious IP addresses.
     */
    private function showTopSuspiciousIPs(string $content): void
    {
        preg_match_all('/"ip_address":"([^"]+)"/', $content, $matches);
        
        if (empty($matches[1])) {
            return;
        }

        $ipCounts = array_count_values($matches[1]);
        arsort($ipCounts);
        $topIPs = array_slice($ipCounts, 0, 5, true);

        if (!empty($topIPs)) {
            $this->line('🚨 Top IPs con actividad sospechosa:');
            $tableData = [];
            foreach ($topIPs as $ip => $count) {
                $tableData[] = [$ip, $count];
            }
            $this->table(['IP Address', 'Eventos'], $tableData);
        }
    }

    /**
     * Check if a log line is within the specified date range.
     */
    private function isLineInDateRange(string $line, Carbon $cutoffDate): bool
    {
        // Extract date from Laravel log format [YYYY-MM-DD HH:MM:SS]
        if (preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $matches)) {
            try {
                $lineDate = Carbon::createFromFormat('Y-m-d H:i:s', $matches[1]);
                return $lineDate->gte($cutoffDate);
            } catch (\Exception $e) {
                return true; // Include line if date parsing fails
            }
        }

        return true; // Include line if no date found
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
