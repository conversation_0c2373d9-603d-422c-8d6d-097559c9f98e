<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class FormLoggerService
{
    /**
     * Log contact form submission.
     */
    public static function logContactForm(Request $request, array $data, bool $success = true, ?string $error = null): void
    {
        try {
            $logData = [
                'form_type' => 'contact_message',
                'timestamp' => now()->toISOString(),
                'success' => $success,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'referer' => $request->header('referer'),
                'session_id' => $request->session()->getId(),
                'data' => [
                    'full_name' => $data['full_name'] ?? null,
                    'email' => $data['email'] ?? null,
                    'phone' => $data['phone'] ?? null,
                    'subject' => $data['subject'] ?? null,
                    'message_length' => isset($data['message']) ? strlen($data['message']) : 0,
                ],
            ];

            if (!$success && $error) {
                $logData['error'] = $error;
                Log::channel('forms')->error('Contact form submission failed', $logData);
            } else {
                Log::channel('forms')->info('Contact form submitted successfully', $logData);
            }

            // Also log to security if there are suspicious patterns
            if (self::detectSuspiciousFormData($data)) {
                Log::channel('security')->warning('Suspicious contact form submission', $logData);
            }

        } catch (\Exception $e) {
            Log::channel('errors')->error('Error logging contact form', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Log newsletter subscription.
     */
    public static function logNewsletterSubscription(Request $request, array $data, bool $success = true, ?string $error = null): void
    {
        try {
            $logData = [
                'form_type' => 'newsletter_subscription',
                'timestamp' => now()->toISOString(),
                'success' => $success,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'referer' => $request->header('referer'),
                'session_id' => $request->session()->getId(),
                'data' => [
                    'full_name' => $data['full_name'] ?? null,
                    'email' => $data['email'] ?? null,
                    'company' => $data['company'] ?? null,
                    'country' => $data['country'] ?? null,
                ],
            ];

            if (!$success && $error) {
                $logData['error'] = $error;
                Log::channel('forms')->error('Newsletter subscription failed', $logData);
            } else {
                Log::channel('forms')->info('Newsletter subscription successful', $logData);
            }

            // Also log to security if there are suspicious patterns
            if (self::detectSuspiciousFormData($data)) {
                Log::channel('security')->warning('Suspicious newsletter subscription', $logData);
            }

        } catch (\Exception $e) {
            Log::channel('errors')->error('Error logging newsletter subscription', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Log general form submission.
     */
    public static function logFormSubmission(string $formType, Request $request, array $data, bool $success = true, ?string $error = null): void
    {
        try {
            $logData = [
                'form_type' => $formType,
                'timestamp' => now()->toISOString(),
                'success' => $success,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'referer' => $request->header('referer'),
                'session_id' => $request->session()->getId(),
                'user_id' => Auth::id(),
                'data' => self::sanitizeFormData($data),
            ];

            if (!$success && $error) {
                $logData['error'] = $error;
                Log::channel('forms')->error("Form submission failed: {$formType}", $logData);
            } else {
                Log::channel('forms')->info("Form submitted successfully: {$formType}", $logData);
            }

        } catch (\Exception $e) {
            Log::channel('errors')->error('Error logging form submission', [
                'form_type' => $formType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Detect suspicious patterns in form data.
     */
    private static function detectSuspiciousFormData(array $data): bool
    {
        $suspiciousPatterns = [
            // SQL Injection patterns
            'union', 'select', 'drop', 'insert', 'update', 'delete', '--', ';',
            // XSS patterns
            '<script', 'javascript:', 'onload=', 'onerror=', '<iframe',
            // Command injection
            '|', '&&', '$(', '`',
            // Path traversal
            '../', '..\\',
            // Common spam patterns
            'viagra', 'cialis', 'casino', 'poker', 'loan', 'mortgage',
        ];

        $dataString = strtolower(implode(' ', array_values($data)));

        foreach ($suspiciousPatterns as $pattern) {
            if (strpos($dataString, $pattern) !== false) {
                return true;
            }
        }

        // Check for excessive length (potential DoS)
        if (strlen($dataString) > 10000) {
            return true;
        }

        // Check for too many URLs
        if (substr_count($dataString, 'http') > 3) {
            return true;
        }

        return false;
    }

    /**
     * Sanitize form data for logging.
     */
    private static function sanitizeFormData(array $data): array
    {
        $sensitiveFields = ['password', 'password_confirmation', 'token', '_token', 'api_key', 'credit_card'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }

        // Truncate long text fields
        foreach ($data as $key => $value) {
            if (is_string($value) && strlen($value) > 1000) {
                $data[$key] = substr($value, 0, 1000) . '... [TRUNCATED]';
            }
        }

        return $data;
    }

    /**
     * Get form submission statistics.
     */
    public static function getFormStatistics(string $formType = null, int $days = 30): array
    {
        try {
            $logPath = storage_path('logs/forms.log');
            
            if (!file_exists($logPath)) {
                return ['total' => 0, 'successful' => 0, 'failed' => 0];
            }

            $logs = file_get_contents($logPath);
            $lines = explode("\n", $logs);
            
            $stats = ['total' => 0, 'successful' => 0, 'failed' => 0];
            $cutoffDate = now()->subDays($days);

            foreach ($lines as $line) {
                if (empty($line)) continue;
                
                // Parse log line (basic parsing)
                if ($formType && strpos($line, $formType) === false) {
                    continue;
                }

                if (strpos($line, 'successfully') !== false) {
                    $stats['successful']++;
                } elseif (strpos($line, 'failed') !== false) {
                    $stats['failed']++;
                }
                
                $stats['total']++;
            }

            return $stats;

        } catch (\Exception $e) {
            Log::channel('errors')->error('Error getting form statistics', [
                'error' => $e->getMessage()
            ]);
            
            return ['total' => 0, 'successful' => 0, 'failed' => 0];
        }
    }
}
