<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\View\Composers\CustomerComposer;
use App\View\Composers\SubscriptionComposer;
use App\View\Composers\FooterComposer;
use App\View\Composers\ContactFormComposer;
use App\View\Composers\CompanyInfoComposer;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        View::composer('web.master.components.customers', CustomerComposer::class);
        View::composer('web.master.components.subscribe', SubscriptionComposer::class);
        View::composer('web.master.components.footer', FooterComposer::class);
        View::composer('web.master.components.contactform', ContactFormComposer::class);
        // View::composer('*', CompanyInfoComposer::class);
        View::composer('web.master.master', CompanyInfoComposer::class);
    }
}
