<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Models\OurCustomerImage;
use App\Models\OurCustomer;
use Illuminate\Support\Facades\View;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $clientes = OurCustomer::get();
        $imgClientes = OurCustomerImage::get();
        View::share('web.master.components.customers', compact('imgClientes', 'clientes'));
    }
}
