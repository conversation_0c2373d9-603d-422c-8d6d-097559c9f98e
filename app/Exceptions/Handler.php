<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            $this->logException($e);
        });
    }

    /**
     * Report or log an exception with enhanced logging.
     */
    public function report(Throwable $e): void
    {
        if ($this->shouldReport($e)) {
            $this->logException($e);
        }

        parent::report($e);
    }

    /**
     * Render an exception into an HTTP response.
     */
    public function render($request, Throwable $e)
    {
        // Log the request that caused the exception
        $this->logExceptionRequest($request, $e);

        // Handle specific exceptions
        if ($e instanceof NotFoundHttpException) {
            return $this->handleNotFound($request, $e);
        }

        if ($e instanceof MethodNotAllowedHttpException) {
            return $this->handleMethodNotAllowed($request, $e);
        }

        if ($e instanceof AuthenticationException) {
            return $this->handleUnauthenticated($request, $e);
        }

        return parent::render($request, $e);
    }

    /**
     * Enhanced exception logging.
     */
    private function logException(Throwable $e): void
    {
        try {
            $context = [
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'timestamp' => now()->toISOString(),
                'environment' => app()->environment(),
                'url' => request()->fullUrl(),
                'method' => request()->method(),
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'user_id' => auth()->id(),
                'session_id' => request()->session() ? request()->session()->getId() : null,
            ];

            // Add request data for debugging (sanitized)
            if (request()->isMethod('POST') || request()->isMethod('PUT')) {
                $context['request_data'] = $this->sanitizeRequestData(request()->all());
            }

            // Determine log level based on exception type
            $logLevel = $this->getLogLevel($e);

            Log::channel('errors')->log($logLevel, 'Exception occurred', $context);

            // Log to security channel for potential security issues
            if ($this->isSecurityRelated($e)) {
                Log::channel('security')->warning('Security-related exception', $context);
            }

        } catch (\Exception $logException) {
            // Fallback logging if our custom logging fails
            Log::emergency('Failed to log exception', [
                'original_exception' => $e->getMessage(),
                'logging_exception' => $logException->getMessage(),
            ]);
        }
    }

    /**
     * Log the request that caused an exception.
     */
    private function logExceptionRequest(Request $request, Throwable $e): void
    {
        try {
            $context = [
                'exception_type' => get_class($e),
                'exception_message' => $e->getMessage(),
                'request_url' => $request->fullUrl(),
                'request_method' => $request->method(),
                'request_ip' => $request->ip(),
                'request_user_agent' => $request->userAgent(),
                'request_referer' => $request->header('referer'),
                'timestamp' => now()->toISOString(),
            ];

            Log::channel('user_activity')->warning('Request caused exception', $context);

        } catch (\Exception $logException) {
            // Silent fail for request logging
        }
    }

    /**
     * Handle 404 Not Found exceptions.
     */
    private function handleNotFound(Request $request, NotFoundHttpException $e)
    {
        // Log potential scanning/probing attempts
        $suspiciousPatterns = [
            'admin', 'wp-admin', 'wp-login', 'phpmyadmin', 'cpanel',
            '.env', '.git', 'config', 'backup', 'test', 'api/v1',
        ];

        $url = strtolower($request->getPathInfo());
        $isSuspicious = false;

        foreach ($suspiciousPatterns as $pattern) {
            if (strpos($url, $pattern) !== false) {
                $isSuspicious = true;
                break;
            }
        }

        if ($isSuspicious) {
            Log::channel('security')->warning('Suspicious 404 request', [
                'url' => $request->fullUrl(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'referer' => $request->header('referer'),
            ]);
        }

        if ($request->expectsJson()) {
            return response()->json(['error' => 'Not Found'], 404);
        }

        return response()->view('errors.404', [], 404);
    }

    /**
     * Handle Method Not Allowed exceptions.
     */
    private function handleMethodNotAllowed(Request $request, MethodNotAllowedHttpException $e)
    {
        Log::channel('security')->info('Method not allowed', [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        if ($request->expectsJson()) {
            return response()->json(['error' => 'Method Not Allowed'], 405);
        }

        return response()->view('errors.405', [], 405);
    }

    /**
     * Handle unauthenticated requests.
     */
    private function handleUnauthenticated(Request $request, AuthenticationException $e)
    {
        if ($request->expectsJson()) {
            return response()->json(['error' => 'Unauthenticated'], 401);
        }

        return redirect()->guest(route('login'));
    }

    /**
     * Determine the appropriate log level for an exception.
     */
    private function getLogLevel(Throwable $e): string
    {
        if ($e instanceof ValidationException) {
            return 'info';
        }

        if ($e instanceof NotFoundHttpException || $e instanceof ModelNotFoundException) {
            return 'warning';
        }

        if ($e instanceof AuthenticationException) {
            return 'warning';
        }

        return 'error';
    }

    /**
     * Determine if an exception is security-related.
     */
    private function isSecurityRelated(Throwable $e): bool
    {
        $securityExceptions = [
            AuthenticationException::class,
            'Illuminate\Auth\Access\AuthorizationException',
            'Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException',
        ];

        return in_array(get_class($e), $securityExceptions);
    }

    /**
     * Sanitize request data for logging.
     */
    private function sanitizeRequestData(array $data): array
    {
        $sensitiveFields = [
            'password', 'password_confirmation', 'token', '_token', 
            'api_key', 'secret', 'private_key', 'credit_card'
        ];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }

        return $data;
    }
}
