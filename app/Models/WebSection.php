<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WebSection extends Model
{
    protected $fillable = [
        'name',
        'description',
        'title',
        'sub_title',
        'content',
        'order',
        'cover_image',
        'page_id',
        'status_id',
        'type_section_id',
    ];
    public function page()
    {
        return $this->belongsTo(WebPage::class);
    }
    public function status()
    {
        return $this->belongsTo(Status::class);
    }
    public function typeSection()
    {
        return $this->belongsTo(WebTypeSection::class);
    }
}
