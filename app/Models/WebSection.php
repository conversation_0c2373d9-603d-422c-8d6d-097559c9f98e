<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WebSection extends Model
{
    protected $fillable = [
        'name',
        'description',
        'title',
        'sub_title',
        'content',
        'order',
        'cover_image',
        'page_id',
        'status_id',
        'type_section_id',
    ];
    public function status()
    {
        return $this->belongsTo(Status::class);
    }
    public function typeSection()
    {
        return $this->belongsTo(WebTypeSection::class);
    }
    public function page()
    {
        return $this->belongsTo(WebPage::class, 'page_id');
    }

    /**
     * Una sección tiene muchas subsecciones.
     * La llave foránea 'section_id' debe ser especificada.
    */
    public function subsections()
    {
        return $this->hasMany(WebSubSection::class, 'section_id');
    }
    public function section_images()
    {
        return $this->hasMany(WebSectionImage::class, 'section_id');
    }
}
