<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OurCustomerImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'image',
        'alt_text',
        'name',
        'website_url',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relación con el cliente
     */
    public function customer()
    {
        return $this->belongsTo(OurCustomer::class, 'customer_id');
    }

    /**
     * Scope para obtener solo imágenes activas
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope para ordenar por sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Accessor para obtener la URL completa de la imagen
     */
    public function getImageUrlAttribute()
    {
        if (empty($this->image)) {
            return asset('img/default-customer.png');
        }

        // Si la ruta ya incluye 'storage/', usarla directamente
        if (str_starts_with($this->image, 'storage/')) {
            return asset($this->image);
        }

        // Si no, agregar 'storage/' al inicio
        return asset('storage/' . $this->image);
    }

    /**
     * Validación rules
     */
    public static function rules(): array
    {
        return [
            'customer_id' => 'required|exists:our_customers,id',
            'image' => 'required|string|max:255',
            'alt_text' => 'nullable|string|max:255',
            'name' => 'nullable|string|max:255',
            'website_url' => 'nullable|url|max:255',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ];
    }
}
