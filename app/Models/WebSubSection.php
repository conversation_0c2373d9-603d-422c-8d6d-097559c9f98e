<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WebSubSection extends Model
{
    protected $fillable = [
        'name',
        'description',
        'cover_image',
        'title',
        'sub_title',
        'type',
        'content',
        'order',
        'button',
        'button_url',
        'button_text',
        'section_id',
        'status_id',
    ];
    public function section()
    {
        return $this->belongsTo(WebSection::class);
    }
    public function status()
    {
        return $this->belongsTo(Status::class);
    }
}
