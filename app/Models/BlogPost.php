<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BlogPost extends Model
{
    protected $fillable = [
        'title',
        'content',
        'image_path',
        'published_at',
        'category_id',
        'status_id',
        'user_id',
    ];
    public function category()
    {
        return $this->belongsTo(BlogCategory::class);
    }
    public function status()
    {
        return $this->belongsTo(Status::class);
    }
}
