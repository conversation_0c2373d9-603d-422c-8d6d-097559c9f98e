<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OurCustomer extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'title',
        'sub_title',
        'content',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relación con las imágenes de clientes
     */
    public function images()
    {
        return $this->hasMany(OurCustomerImage::class, 'customer_id');
    }

    /**
     * Obtener solo las imágenes activas ordenadas
     */
    public function activeImages()
    {
        return $this->hasMany(OurCustomerImage::class, 'customer_id')
                    ->where('is_active', true)
                    ->orderBy('sort_order');
    }

    /**
     * Scope para obtener solo clientes activos
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope para ordenar por sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }
}
