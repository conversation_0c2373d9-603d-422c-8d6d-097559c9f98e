<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Applicant extends Model
{
    protected $fillable = [
        'user_id',
        'participant_profile_id',
        'employment_id',
        'status_id',
    ];

    public function employment()
    {
        return $this->belongsTo(Employment::class);
    }
    public function status()
    {
        return $this->belongsTo(Status::class);
    }
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function participantProfile()
    {
        return $this->belongsTo(ParticipantProfile::class);
    }
    
}
