<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Applicant extends Model
{
    protected $fillable = [
        'name',
        'last_name',
        'email',
        'phone',
        'social_networks',
        'description',
        'cv_file',
        'job_id',
        'status_id',
    ];

    public function job()
    {
        return $this->belongsTo(Job::class);
    }
    public function status()
    {
        return $this->belongsTo(Status::class);
    }
}
