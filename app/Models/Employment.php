<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Employment extends Model
{
    protected $fillable = [
        'title',
        'subtitle',
        'short_description',
        'slug',
        'description',
        'cover_image',
        'status_id',
    ];  

    public function status()
    {
        return $this->belongsTo(Status::class);
    }
    public function applicants()
    {
        return $this->hasMany(Applicant::class);
    }
}
