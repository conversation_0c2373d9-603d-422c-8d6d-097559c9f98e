<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('web_page_images', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('location');
            $table->string('alt')->nullable();
            $table->foreignId('page_id')->constrained('web_pages')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('web_page_images');
    }
};
