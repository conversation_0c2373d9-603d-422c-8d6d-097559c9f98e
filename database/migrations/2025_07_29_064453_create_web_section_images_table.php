<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('web_section_images', function (Blueprint $table) {
            $table->id();
            $table->foreignId('section_id')->constrained('web_sections')->cascadeOnDelete();
            $table->foreignId('page_image_id')->constrained('web_page_images')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('web_section_images');
    }
};
