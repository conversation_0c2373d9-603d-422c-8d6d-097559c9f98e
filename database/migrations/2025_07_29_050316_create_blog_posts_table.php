<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('blog_posts', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->longText('content');
            $table->string('slug')->unique()->nullable();
            $table->string('image_path')->nullable();
            $table->date('published_at');
            // $table->unsignedTinyInteger('category_id');
            // $table->unsignedTinyInteger('status_id');
            // $table->unsignedTinyInteger('user_id');
            // $table->foreign('category_id')->references('id')->on('blog_categories');
            // $table->foreign('status_id')->references('id')->on('statuses');
            // $table->foreign('user_id')->references('id')->on('users');
            $table->foreignId('category_id')->constrained('blog_categories')->cascadeOnDelete();
            $table->foreignId('status_id')->constrained('statuses')->cascadeOnDelete();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete();
            $table->integer('view_count')->default(0);
            $table->timestamps();           
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blog_posts');
    }
};
