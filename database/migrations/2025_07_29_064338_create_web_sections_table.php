<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('web_sections', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable(); // nombre de la section
            $table->longText('description')->nullable(); //esta columna ayuda a gestionar y/o identificar que se crea/edita/elimina
            $table->string('title');
            $table->string('sub_title')->nullable();
            $table->longText('content')->nullable();
            $table->integer('order');
            $table->string('cover_image')->nullable();
            $table->foreignId('page_id')->constrained('web_pages')->cascadeOnDelete();
            $table->foreignId('status_id')->constrained('statuses')->cascadeOnDelete();
            $table->foreignId('type_section_id')->constrained('web_type_sections')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('web_sections');
    }
};
