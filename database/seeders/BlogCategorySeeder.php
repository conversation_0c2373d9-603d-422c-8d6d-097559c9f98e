<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\BlogCategory;

class BlogCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        BlogCategory::create([
            'name' => 'Noticias',
            'description' => 'Noticias',
            'slug' => 'noticias',
        ]);
        BlogCategory::create([
            'name' => 'Eventos',
            'description' => 'Eventos',
            'slug' => 'eventos',
        ]);
        BlogCategory::create([
            'name' => 'Artículos',
            'description' => 'Artículos',
            'slug' => 'articulos',
        ]);
        BlogCategory::create([
            'name' => 'Economía',
            'description' => 'Economía',
            'slug' => 'economia',
        ]);
        BlogCategory::create([
            'name' => 'Tecnología',
            'description' => 'Tecnología',
            'slug' => 'tecnologia',
        ]);
    }
}
