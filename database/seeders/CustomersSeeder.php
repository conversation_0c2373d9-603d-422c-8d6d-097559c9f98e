<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\OurCustomer;
use App\Models\OurCustomerImage;

class CustomersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Crear o actualizar la información principal de clientes
        $customer = OurCustomer::updateOrCreate(
            ['name' => 'main'],
            [
                'title' => 'Nuestros Clientes',
                'sub_title' => 'Empresas que Confían en Nosotros',
                'description' => 'Trabajamos con organizaciones líderes que no solo buscan la excelencia, sino que la construyen activamente a través del desarrollo estratégico de su talento humano. Son empresas que invierten en su activo más valioso, logrando resultados extraordinarios y forjando el futuro de su sector con nuestra ayuda.',
                'content' => 'Contenido adicional sobre nuestros clientes',
                'is_active' => true,
                'sort_order' => 1,
            ]
        );

        // Array de clientes con sus datos
        $clientsData = [
            ['name' => 'COMISCA', 'image' => 'img/customers/ayd_0000_comisca.jpg', 'alt' => 'COMISCA'],
            ['name' => 'COMEDICA', 'image' => 'img/customers/ayd_0001_comedica.jpg', 'alt' => 'COMEDICA'],
            ['name' => 'CNR', 'image' => 'img/customers/ayd_0002_cnr.jpg', 'alt' => 'CNR'],
            ['name' => 'CNJ', 'image' => 'img/customers/ayd_0003_cnj.jpg', 'alt' => 'CNJ'],
            ['name' => 'CNDC', 'image' => 'img/customers/ayd_0004_cndc.jpg', 'alt' => 'CNDC'],
            ['name' => 'CLAC', 'image' => 'img/customers/ayd_0005_clac.jpg', 'alt' => 'CLAC'],
            ['name' => 'CEPA', 'image' => 'img/customers/ayd_0006_cepa.jpg', 'alt' => 'CEPA'],
            ['name' => 'CENTA', 'image' => 'img/customers/ayd_0007_centa.jpg', 'alt' => 'CENTA'],
            ['name' => 'IGD', 'image' => 'img/customers/ayd_0008_igd.jpg', 'alt' => 'IGD'],
            ['name' => 'FOSALUD', 'image' => 'img/customers/ayd_0009_fosalud.jpg', 'alt' => 'FOSALUD'],
            ['name' => 'IBTCI', 'image' => 'img/customers/ayd_0010_ibtci.jpg', 'alt' => 'IBTCI'],
            ['name' => 'CEL', 'image' => 'img/customers/ayd_0011_cel.jpg', 'alt' => 'CEL'],
            ['name' => 'IAIP', 'image' => 'img/customers/ayd_0012_iaip.jpg', 'alt' => 'IAIP'],
            ['name' => 'FOMILENIO', 'image' => 'img/customers/ayd_0013_fomilenioll.jpg', 'alt' => 'FOMILENIO'],
            ['name' => 'PBS', 'image' => 'img/customers/ayd_0014_pbs.jpg', 'alt' => 'PBS'],
            ['name' => 'IICA', 'image' => 'img/customers/ayd_0015_iica.jpg', 'alt' => 'IICA'],
            ['name' => 'Panadería El Rosario', 'image' => 'img/customers/ayd_0016_panaderiaelrosario.jpg', 'alt' => 'Panadería El Rosario'],
            ['name' => 'INSAFORP', 'image' => 'img/customers/ayd_0017_insaforp.jpg', 'alt' => 'INSAFORP'],
            ['name' => 'OMS', 'image' => 'img/customers/ayd_0018_oms.jpg', 'alt' => 'OMS'],
            ['name' => 'ISDEM', 'image' => 'img/customers/ayd_0019_isdem.jpg', 'alt' => 'ISDEM'],
            ['name' => 'OJ', 'image' => 'img/customers/ayd_0020_oj.jpg', 'alt' => 'OJ'],
            ['name' => 'Hospital Pro Familia', 'image' => 'img/customers/ayd_0021_hopitalprofamilia.jpg', 'alt' => 'Hospital Pro Familia'],
            ['name' => 'IPSFA', 'image' => 'img/customers/ayd_0022_ipsfa.jpg', 'alt' => 'IPSFA'],
            ['name' => 'ODS', 'image' => 'img/customers/ayd_0023_ods.jpg', 'alt' => 'ODS'],
            ['name' => 'ISEADE', 'image' => 'img/customers/ayd_0024_iseade.jpg', 'alt' => 'ISEADE'],
            ['name' => 'ISNA', 'image' => 'img/customers/ayd_0025_isna.jpg', 'alt' => 'ISNA'],
            ['name' => 'MINEC', 'image' => 'img/customers/ayd_0026_minec.jpg', 'alt' => 'MINEC'],
            ['name' => 'Maristas', 'image' => 'img/customers/ayd_0027_maristas.jpg', 'alt' => 'Maristas'],
            ['name' => 'MOPT', 'image' => 'img/customers/ayd_0028_mopt.jpg', 'alt' => 'MOPT'],
            ['name' => 'MARN', 'image' => 'img/customers/ayd_0029_marn.jpg', 'alt' => 'MARN'],
            ['name' => 'CC Zacatecoluca', 'image' => 'img/customers/ayd_0030_cczacatecoluca.jpg', 'alt' => 'CC Zacatecoluca'],
            ['name' => 'Glasswing', 'image' => 'img/customers/ayd_0031_glasswing.jpg', 'alt' => 'Glasswing'],
            ['name' => 'FISDL', 'image' => 'img/customers/ayd_0032_fisdl.jpg', 'alt' => 'FISDL'],
            ['name' => 'FUSAI', 'image' => 'img/customers/ayd_0033_fusai.jpg', 'alt' => 'FUSAI'],
            ['name' => 'FOSEP', 'image' => 'img/customers/ayd_0034_fosep.jpg', 'alt' => 'FOSEP'],
            ['name' => 'FUNDAUNGO', 'image' => 'img/customers/ayd_0035_fundaungo.jpg', 'alt' => 'FUNDAUNGO'],
            ['name' => 'CCR', 'image' => 'img/customers/ayd_0036_ccr.jpg', 'alt' => 'CCR'],
            ['name' => 'FIAES', 'image' => 'img/customers/ayd_0037_fiaes.jpg', 'alt' => 'FIAES'],
            ['name' => 'FUNDASIL', 'image' => 'img/customers/ayd_0038_fundasil.jpg', 'alt' => 'FUNDASIL'],
            ['name' => 'CASF', 'image' => 'img/customers/ayd_0039_casf.jpg', 'alt' => 'CASF'],
            ['name' => 'FOVIAL', 'image' => 'img/customers/ayd_0040_fovial.jpg', 'alt' => 'FOVIAL'],
        ];

        // Crear las imágenes de clientes
        foreach ($clientsData as $index => $clientData) {
            OurCustomerImage::updateOrCreate(
                [
                    'customer_id' => $customer->id,
                    'name' => $clientData['name']
                ],
                [
                    'image' => $clientData['image'],
                    'alt_text' => $clientData['alt'],
                    'website_url' => null, // Puedes agregar URLs específicas aquí
                    'is_active' => true,
                    'sort_order' => $index + 1,
                ]
            );
        }

        $this->command->info('✅ Clientes creados exitosamente: ' . count($clientsData) . ' imágenes');
    }
}
