<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $superAdminRole = Role::create(['name' => 'Super Admin']);
        $adminRole = Role::create(['name' => 'Administrador']);
        $commentatorRole = Role::create(['name' => 'Comentador']);
        $applicantRole = Role::create(['name' => 'Participante']);
        $blockUserRole = Role::create(['name' => 'Usuario bloqueado']);
        //POST
        // Permission::firstOrCreate(['name' => 'crear posts'])->syncRoles([$adminRole, $superAdminRole]);
        // Permission::firstOrCreate(['name' => 'ver posts'])->syncRoles([$adminRole, $superAdminRole]);
        // Permission::firstOrCreate(['name' => 'editar posts'])->syncRoles([$adminRole, $superAdminRole]);
        // //CATEGORIAS
        // Permission::firstOrCreate(['name' => 'crear categorias'])->syncRoles([$adminRole, $superAdminRole]);
        // Permission::firstOrCreate(['name' => 'ver etiquetas'])->syncRoles([$adminRole, $superAdminRole]);
        // Permission::firstOrCreate(['name' => 'editar etiquetas'])->syncRoles([$adminRole, $superAdminRole]);
        // //ETIQUETAS
        // Permission::firstOrCreate(['name' => 'crear etiquetas'])->syncRoles([$adminRole, $superAdminRole]);
        // Permission::firstOrCreate(['name' => 'ver etiquetas'])->syncRoles([$adminRole, $superAdminRole]);
        // Permission::firstOrCreate(['name' => 'editar etiquetas'])->syncRoles([$adminRole, $superAdminRole]);
        // //USUARIOS
        // Permission::firstOrCreate(['name' => 'crear usuarios'])->syncRoles([$adminRole, $superAdminRole]);
        // Permission::firstOrCreate(['name' => 'ver usuarios'])->syncRoles([$adminRole, $superAdminRole]);
        // Permission::firstOrCreate(['name' => 'editar usuarios'])->syncRoles([$adminRole, $superAdminRole]);
        
    }
}
