<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('statuses')->insert([
            ['name' => 'En línea', 'description' => 'Visible para todos'],
            ['name' => 'Borrador', 'description' => 'No visible para nadie'],
            ['name' => 'En revisión', 'description' => 'Visible para revisión'],
            ['name' => 'Activo', 'description' => 'Visible para todos, se utiliza para usuarios'],
            ['name' => 'Inactivo', 'description' => 'No visible para nadie, se utiliza para usuarios'],
            ['name' => 'Bloqueado', 'description' => 'No tiene acceso al sistema, se utiliza para usuarios'],
        ]);
    }
}
