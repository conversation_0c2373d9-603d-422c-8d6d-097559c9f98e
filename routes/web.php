<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PagesAdminController;
use App\Http\Controllers\NewsletterSubscriberController;
use App\Http\Controllers\ContactMessageController;
use App\Http\Controllers\BlogController;

// Route::get('/dashboard', function () {
//     return view('dashboard');
// })->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

Route::middleware('guest')->group(function () {
    Route::get('/', function () {
        return view('web.pages.home.home');
    })->name('home');

    Route::get('/nosotros', function () {
        return view('web.pages.about.index');
    })->name('nosotros');

    Route::get('/consultorias', function () {
        return view('web.pages.consulting.index');
    })->name('consultorias');

    Route::get('/formacion-profesional', function () {
        return view('web.pages.professional.index');
    })->name('formacion-profesional');

    Route::get('/contactanos', function () {
        return view('web.pages.contact.index');
    })->name('contactanos');
    Route::post('/newSubscriber', [NewsletterSubscriberController::class, 'newSubscriber'])->name('newsletter.subscriber');
    Route::post('/newContactMessage', [ContactMessageController::class, 'newContactMessage'])->name('contact.message');
});


Route::middleware('auth')->prefix('admin')->group(function () {
    Route::get('/inicio', [PagesAdminController::class, 'index'])->name('admin.home');
    Route::get('/profile', [PagesAdminController::class, 'ProfilePage'])->name('admin.profile');
    Route::get('/empleos', [PagesAdminController::class, 'RecruitmentPage'])->name('admin.recruitment');
    Route::get('/users', [PagesAdminController::class, 'UserPage'])->name('admin.users');
    Route::get('/paginas/inicio', [PagesAdminController::class, 'HomePage'])->name('admin.pages.home');
    Route::get('/paginas/nosotros', [PagesAdminController::class, 'AboutPage'])->name('admin.pages.about');
    Route::get('/paginas/consultorias', [PagesAdminController::class, 'ConsultingPage'])->name('admin.pages.consulting');
    Route::get('/paginas/formacion-profesional', [PagesAdminController::class, 'ProfessionalPage'])->name('admin.pages.professional');
    Route::get('/paginas/contactanos', [PagesAdminController::class, 'ContactPage'])->name('admin.pages.contact');

    //suscriptores
    Route::get('/suscripciones', [NewsletterSubscriberController::class, 'showSubscribers'])->name('admin.newsletter');
    Route::get('/suscripciones/editar/{id}', [NewsletterSubscriberController::class, 'editSubscriber'])->name('admin.edit.subscribers');

    //mensajes
    Route::get('/mensajes', [ContactMessageController::class, 'showMessages'])->name('admin.contactmessage');
    Route::get('/mensajes/ver/{id}', [ContactMessageController::class, 'viewMessage'])->name('admin.view.message');

    //blog
    Route::get('/blog', [BlogController::class, 'blogView'])->name('admin.blog');
    Route::get('/blog/nuevo', [BlogController::class, 'createNewPostView'])->name('admin.view.new.post');
    Route::get('/blog/{id}', [BlogController::class, 'blogPostView'])->name('admin.blog.view.post');
    Route::post('/blog/nuevo-post', [BlogController::class, 'newPost'])->name('posts.store');
    Route::put('/blog/actualizar-post/{id}', [BlogController::class, 'update'])->name('posts.update');
    // Route::post('/upload-temp-image', [BlogController::class, 'storeTemp'])->name('image.upload.temp');

});

require __DIR__.'/auth.php';
