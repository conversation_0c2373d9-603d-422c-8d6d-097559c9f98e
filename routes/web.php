<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

Route::middleware('guest')->group(function () {
    Route::get('/', function () {
        return view('web.pages.home.home');
    })->name('home');

    Route::get('/nosotros', function () {
        return view('web.pages.about.index');
    })->name('nosotros');

    Route::get('/consultorias', function () {
        return view('web.pages.consulting.index');
    })->name('consultorias');

    Route::get('/formacion-profesional', function () {
        return view('web.pages.professional.index');
    })->name('formacion-profesional');

    Route::get('/contactanos', function () {
        return view('web.pages.contact.index');
    })->name('contactanos');

    Route::get('/empleos', function () {
        return view('admin.index');
    })->name('empleos');
});

require __DIR__.'/auth.php';
