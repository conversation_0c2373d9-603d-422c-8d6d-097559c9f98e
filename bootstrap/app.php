<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use App\Http\Middleware\LogUserActivity;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Register the user activity logging middleware globally
        $middleware->web(append: [
            LogUserActivity::class,
        ]);

        // You can also register it for specific routes or groups
        $middleware->alias([
            'log.activity' => LogUserActivity::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        // Custom exception handling is already configured in app/Exceptions/Handler.php
        // <PERSON><PERSON> will automatically use it
    })->create();
