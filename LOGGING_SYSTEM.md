# Sistema de Logging Integral - Laravel

## 📋 Descripción General

Este sistema de logging integral proporciona un registro completo de todas las actividades importantes en la aplicación Laravel, incluyendo formularios, errores, seguridad y actividad de usuarios.

## 🗂️ Estructura de Archivos de Log

Los logs se almacenan en `storage/logs/` con rotación diaria:

- **`forms.log`** - Registro de envíos de formularios (contacto y newsletter)
- **`security.log`** - Eventos de seguridad y actividad sospechosa
- **`user_activity.log`** - Actividad general de usuarios en el sitio
- **`errors.log`** - Errores de aplicación y excepciones
- **`api.log`** - Actividad de API (para uso futuro)

## 🔧 Componentes del Sistema

### 1. Configuración de Logging (`config/logging.php`)

Se agregaron canales personalizados:
```php
'forms' => [
    'driver' => 'daily',
    'path' => storage_path('logs/forms.log'),
    'level' => env('LOG_LEVEL', 'info'),
    'days' => env('LOG_DAILY_DAYS', 30),
],
// ... otros canales
```

### 2. Middleware de Actividad (`app/Http/Middleware/LogUserActivity.php`)

**Funcionalidades:**
- Registra todas las solicitudes HTTP
- Detecta patrones de actividad sospechosa
- Sanitiza datos sensibles
- Controla límites de solicitudes por IP

**Patrones de Seguridad Detectados:**
- Inyección SQL
- Cross-Site Scripting (XSS)
- Traversal de directorios
- Inyección de comandos
- Códigos de estado sospechosos (403, 404, 429, 500, 503)

### 3. Servicio de Logging de Formularios (`app/Services/FormLoggerService.php`)

**Métodos principales:**
- `logContactForm()` - Registra envíos del formulario de contacto
- `logNewsletterSubscription()` - Registra suscripciones al newsletter
- `logFormSubmission()` - Método genérico para cualquier formulario
- `getFormStatistics()` - Obtiene estadísticas de formularios

### 4. Exception Handler Personalizado (`app/Exceptions/Handler.php`)

**Características:**
- Logging detallado de excepciones
- Manejo específico de errores 404, 405, autenticación
- Detección de intentos de escaneo/sondeo
- Logging de contexto completo (IP, user agent, datos de solicitud)

### 5. Controladores Actualizados

**ContactMessageController:**
- Logging de intentos de envío
- Registro de éxitos y fallos
- Detección de contenido sospechoso

**NewsletterSubscriberController:**
- Logging de suscripciones
- Registro de intentos duplicados
- Manejo de errores con logging

## 📊 Comando de Estadísticas

### Uso del Comando `logs:stats`

```bash
# Ver estadísticas de todos los logs de los últimos 7 días
php artisan logs:stats

# Ver estadísticas específicas
php artisan logs:stats --type=forms --days=30
php artisan logs:stats --type=security --days=7
php artisan logs:stats --type=errors --days=1
```

**Opciones disponibles:**
- `--days=N` - Número de días a analizar (default: 7)
- `--type=TYPE` - Tipo de log (all, forms, security, errors, user_activity)

## 🔍 Información Registrada

### Formularios
```json
{
  "form_type": "contact_message",
  "timestamp": "2024-01-15T10:30:00Z",
  "success": true,
  "ip_address": "*************",
  "user_agent": "Mozilla/5.0...",
  "data": {
    "full_name": "Juan Pérez",
    "email": "<EMAIL>",
    "message_length": 150
  }
}
```

### Actividad de Usuario
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "user_id": null,
  "user_name": "Guest",
  "ip_address": "*************",
  "method": "GET",
  "url": "https://example.com/nosotros",
  "status_code": 200
}
```

### Eventos de Seguridad
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "event_type": "suspicious_activity",
  "ip_address": "*************",
  "pattern_detected": "sql_injection",
  "request_data": "[SANITIZED]"
}
```

## 🚨 Alertas de Seguridad

El sistema detecta automáticamente:

1. **Inyección SQL**: `union`, `select`, `drop`, `--`, `;`
2. **XSS**: `<script`, `javascript:`, `onload=`
3. **Path Traversal**: `../`, `/etc/passwd`
4. **Command Injection**: `|`, `&&`, `$(`, `` ` ``
5. **Spam**: Patrones comunes de spam
6. **DoS**: Solicitudes excesivas desde una IP

## 📈 Monitoreo y Mantenimiento

### Rotación de Logs
- Los logs rotan diariamente
- Retención configurable (default: 30-90 días según el tipo)
- Limpieza automática de logs antiguos

### Configuración de Retención
```env
LOG_DAILY_DAYS=30  # Días de retención para logs generales
```

### Monitoreo de Espacio
```bash
# Ver tamaño de logs
du -sh storage/logs/

# Ver logs más recientes
tail -f storage/logs/forms.log
tail -f storage/logs/security.log
```

## 🔧 Configuración Adicional

### Variables de Entorno
```env
LOG_LEVEL=info
LOG_DAILY_DAYS=30
APP_DEBUG=false  # En producción
```

### Personalización de Patrones de Seguridad

Editar `app/Http/Middleware/LogUserActivity.php`:
```php
private function isSuspiciousActivity(Request $request, Response $response): bool
{
    // Agregar nuevos patrones aquí
    $suspiciousPatterns = [
        'custom_pattern' => ['nuevo_patron', 'otro_patron'],
        // ...
    ];
}
```

## 📋 Checklist de Implementación

- [x] Configuración de canales de logging
- [x] Middleware de actividad de usuario
- [x] Servicio de logging de formularios
- [x] Exception handler personalizado
- [x] Actualización de controladores
- [x] Registro de middleware
- [x] Vistas de error personalizadas
- [x] Comando de estadísticas
- [x] Documentación completa

## 🚀 Próximos Pasos

1. **Monitoreo en Tiempo Real**: Implementar dashboard de logs
2. **Alertas por Email**: Notificaciones automáticas para eventos críticos
3. **Integración con Slack**: Alertas de seguridad en tiempo real
4. **Análisis de Tendencias**: Gráficos y reportes automáticos
5. **Backup de Logs**: Respaldo automático a almacenamiento externo

## 📞 Soporte

Para consultas sobre el sistema de logging:
- Revisar logs en `storage/logs/`
- Ejecutar `php artisan logs:stats` para diagnósticos
- Verificar configuración en `config/logging.php`
